# RK3588 SPI0 设备树源文件修复指南

## 问题分析

通过深度分析发现，RK3588 SPI0的问题根源在于设备树中的pinctrl配置不完整：

1. **CLK/MISO/MOSI引脚显示为"GPIO UNCLAIMED"**
2. **只有CS引脚被正确配置**
3. **pinctrl配置缺少正确的引脚复用设置**

## 解决方案

需要修改设备树源文件中的SPI0和pinctrl配置。

### 1. 查找设备树源文件

常见位置：
- `/boot/dts/` 
- `/usr/src/linux/arch/arm64/boot/dts/rockchip/`
- 厂商提供的SDK中

### 2. 修改pinctrl配置

在设备树源文件中找到pinctrl节点，添加或修改SPI0的pinctrl配置：

```dts
&pinctrl {
    spi0 {
        spi0m3_pins: spi0m3-pins {
            rockchip,pins =
                /* spi0_clk_m3: GPIO3_C1 */
                <3 RK_PC1 4 &pcfg_pull_up_drv_level_2>,
                /* spi0_miso_m3: GPIO3_C2 */
                <3 RK_PC2 4 &pcfg_pull_up>,
                /* spi0_mosi_m3: GPIO3_C3 */
                <3 RK_PC3 4 &pcfg_pull_up_drv_level_2>;
        };
        
        spi0m3_cs0: spi0m3-cs0 {
            rockchip,pins =
                /* spi0_cs0_m3: GPIO3_C4 */
                <3 RK_PC4 4 &pcfg_pull_up>;
        };
    };
};
```

### 3. 修改SPI0节点配置

```dts
&spi0 {
    status = "okay";
    pinctrl-names = "default";
    pinctrl-0 = <&spi0m3_pins>, <&spi0m3_cs0>;
    
    spidev@0 {
        compatible = "rohm,dh2228fv";
        reg = <0>;
        spi-max-frequency = <50000000>;
    };
};
```

### 4. 确保GPIO3配置正确

```dts
&gpio3 {
    status = "okay";
};
```

## 编译和应用步骤

### 1. 安装设备树编译器
```bash
sudo apt-get install device-tree-compiler
```

### 2. 编译设备树
```bash
# 如果是单独的dts文件
dtc -I dts -O dtb -o rk3588-board.dtb rk3588-board.dts

# 如果是内核源码树
make ARCH=arm64 CROSS_COMPILE=aarch64-linux-gnu- dtbs
```

### 3. 替换设备树文件
```bash
# 备份原文件
sudo cp /boot/rk3588-board.dtb /boot/rk3588-board.dtb.backup

# 替换新文件
sudo cp rk3588-board.dtb /boot/

# 重启系统
sudo reboot
```

## 验证修复效果

重启后运行以下命令验证：

```bash
# 检查pinctrl状态
cat /sys/kernel/debug/pinctrl/pinctrl-rockchip-pinctrl/pinmux-pins | grep -E "pin (121|122|123|124)"

# 检查GPIO状态  
cat /sys/kernel/debug/gpio | grep -A10 "gpiochip3:"

# 运行SPI测试
sudo ./simple_spi_test
```

## 常见问题和解决方案

### 1. 找不到设备树源文件
- 联系板卡厂商获取完整的BSP包
- 查看bootloader配置确定使用的设备树文件名
- 使用`dtc -I dtb -O dts`反编译现有的dtb文件

### 2. 编译错误
- 检查语法是否正确
- 确保包含了必要的头文件
- 验证引脚编号和功能复用号是否正确

### 3. 修改后仍无效
- 确认bootloader是否加载了新的设备树文件
- 检查是否有其他地方覆盖了配置
- 验证GPIO3控制器是否正常工作

## 引脚复用参考

RK3588 GPIO3_C组引脚复用表：

| 引脚 | GPIO | 功能0 | 功能1 | 功能2 | 功能3 | 功能4 |
|------|------|-------|-------|-------|-------|-------|
| GPIO3_C1 | 121 | GPIO | - | - | - | SPI0_CLK_M3 |
| GPIO3_C2 | 122 | GPIO | - | - | - | SPI0_MISO_M3 |
| GPIO3_C3 | 123 | GPIO | - | - | - | SPI0_MOSI_M3 |
| GPIO3_C4 | 124 | GPIO | - | - | - | SPI0_CS0_M3 |

## 寄存器配置参考

如果需要直接操作寄存器：

```bash
# GPIO3C功能选择寄存器: 0xFD5F4020
# GPIO3C1[7:4] = 4 (SPI0_CLK)
# GPIO3C2[11:8] = 4 (SPI0_MISO)  
# GPIO3C3[15:12] = 4 (SPI0_MOSI)
# GPIO3C4[19:16] = 4 (SPI0_CS0)

devmem 0xFD5F4020 32 0x44440000
```

## 总结

通过正确配置设备树中的pinctrl设置，可以解决RK3588 SPI0只有CS信号输出的问题。关键是确保所有SPI引脚都正确复用到SPI功能，而不是保持为GPIO模式。
