#!/bin/bash

echo "=== RK3588 SPI深度分析脚本 ==="
echo ""

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}1. 系统信息${NC}"
echo "内核版本: $(uname -r)"
echo "架构: $(uname -m)"
echo ""

echo -e "${BLUE}2. SPI控制器寄存器分析${NC}"
echo "SPI0控制器地址: 0xfeb00000"

# 检查SPI控制器是否被映射
if [ -d "/sys/devices/platform/feb00000.spi" ]; then
    echo -e "${GREEN}✓ SPI0控制器设备存在${NC}"
    echo "设备路径: /sys/devices/platform/feb00000.spi"
    
    # 检查SPI控制器状态
    echo "SPI控制器状态:"
    ls -la /sys/devices/platform/feb00000.spi/ | grep -E "(modalias|uevent|driver)"
    
    if [ -f "/sys/devices/platform/feb00000.spi/modalias" ]; then
        echo "modalias: $(cat /sys/devices/platform/feb00000.spi/modalias)"
    fi
else
    echo -e "${RED}✗ SPI0控制器设备不存在${NC}"
fi

echo ""
echo -e "${BLUE}3. 设备树深度分析${NC}"

# 检查SPI节点的详细配置
echo "SPI0设备树配置:"
echo "Status: $(xxd -l 10 /sys/firmware/devicetree/base/spi@feb00000/status 2>/dev/null | cut -d' ' -f2-9 | tr -d '\n')"

# 检查pinctrl配置
echo ""
echo "Pinctrl配置:"
if [ -f "/sys/firmware/devicetree/base/spi@feb00000/pinctrl-0" ]; then
    echo "pinctrl-0存在"
    xxd /sys/firmware/devicetree/base/spi@feb00000/pinctrl-0 | head -2
else
    echo -e "${RED}pinctrl-0不存在${NC}"
fi

# 检查时钟配置
echo ""
echo "时钟配置:"
if [ -f "/sys/firmware/devicetree/base/spi@feb00000/clocks" ]; then
    echo "clocks配置存在"
    xxd /sys/firmware/devicetree/base/spi@feb00000/clocks | head -2
else
    echo -e "${RED}clocks配置不存在${NC}"
fi

echo ""
echo -e "${BLUE}4. Pinctrl子系统分析${NC}"

# 检查pinctrl状态
echo "Pinctrl设备:"
ls /sys/class/pinctrl/

echo ""
echo "SPI相关的pinctrl状态:"
cat /sys/kernel/debug/pinctrl/pinctrl-rockchip-pinctrl/pinmux-pins | grep -E "pin (121|122|123|124)" | while read line; do
    pin_num=$(echo "$line" | grep -o "pin [0-9]*" | cut -d' ' -f2)
    if echo "$line" | grep -q "UNCLAIMED"; then
        echo -e "${RED}引脚$pin_num: UNCLAIMED${NC}"
    else
        echo -e "${GREEN}引脚$pin_num: 已配置${NC}"
    fi
    echo "  $line"
done

echo ""
echo -e "${BLUE}5. GPIO子系统分析${NC}"

# 检查GPIO控制器
echo "GPIO控制器状态:"
cat /sys/kernel/debug/gpio | grep -A15 "gpiochip3:"

echo ""
echo -e "${BLUE}6. SPI驱动分析${NC}"

# 检查SPI驱动状态
echo "已加载的SPI相关模块:"
lsmod | grep -i spi || echo "未找到SPI模块"

echo ""
echo "SPI设备文件:"
ls -la /dev/spidev* 2>/dev/null || echo "未找到SPI设备文件"

echo ""
echo -e "${BLUE}7. 内核消息分析${NC}"
echo "SPI相关内核消息:"
dmesg | grep -i spi | tail -10

echo ""
echo -e "${BLUE}8. 时钟系统分析${NC}"
echo "SPI相关时钟:"
find /sys/kernel/debug/clk -name "*spi*" 2>/dev/null | head -5

echo ""
echo -e "${BLUE}9. 中断分析${NC}"
echo "SPI相关中断:"
cat /proc/interrupts | grep -i spi || echo "未找到SPI中断"

echo ""
echo -e "${BLUE}10. 问题根因分析${NC}"

# 分析可能的问题
echo -e "${YELLOW}可能的问题原因:${NC}"

# 检查pinctrl是否正确配置
unclaimed_pins=$(cat /sys/kernel/debug/pinctrl/pinctrl-rockchip-pinctrl/pinmux-pins | grep -E "pin (121|122|123)" | grep -c "UNCLAIMED")
if [ "$unclaimed_pins" -gt 0 ]; then
    echo -e "${RED}1. Pinctrl配置问题: $unclaimed_pins 个SPI引脚未被正确配置${NC}"
    echo "   解决方案: 修改设备树pinctrl配置"
fi

# 检查SPI驱动是否正确加载
if ! lsmod | grep -q spi; then
    echo -e "${RED}2. SPI驱动未加载${NC}"
    echo "   解决方案: 检查内核配置和模块加载"
fi

# 检查设备树配置
if [ ! -f "/sys/firmware/devicetree/base/spi@feb00000/pinctrl-0" ]; then
    echo -e "${RED}3. 设备树pinctrl-0配置缺失${NC}"
    echo "   解决方案: 添加正确的pinctrl配置到设备树"
fi

echo ""
echo -e "${BLUE}11. 建议的修复步骤${NC}"
echo "1. 检查并修复设备树pinctrl配置"
echo "2. 确保SPI时钟正确配置"
echo "3. 验证GPIO功能复用设置"
echo "4. 重新编译并加载设备树"
echo "5. 重启系统验证修复效果"
