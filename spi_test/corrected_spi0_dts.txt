// 修正后的SPI0设备树配置
// 替换您原有的pinctrl和spi0配置

&pinctrl {
    hym8563 {
        hym8563_int: hym8563-int {
            rockchip,pins = <0 RK_PB0 RK_FUNC_GPIO &pcfg_pull_up>;
        };
    };

    headphone {
        hp_det: hp-det {
            rockchip,pins = <1 RK_PC4 RK_FUNC_GPIO &pcfg_pull_none>;
        };
    };

    hdmi {
        hdmirx_det: hdmirx-det {
            rockchip,pins = <1 RK_PD5 RK_FUNC_GPIO &pcfg_pull_up>;
        };
    };
    
    // 修正后的SPI0引脚配置
    spi0 {
        spi0m3_pins: spi0m3-pins {
            rockchip,pins =
                /* SPI0_CLK_M3: GPIO3_C1, 功能号4 */
                <3 RK_PC1 4 &pcfg_pull_up_drv_level_2>,
                /* SPI0_MISO_M3: GPIO3_C2, 功能号4 */
                <3 RK_PC2 4 &pcfg_pull_up>,
                /* SPI0_MOSI_M3: GPIO3_C3, 功能号4 */
                <3 RK_PC3 4 &pcfg_pull_up_drv_level_2>;
        };
        
        spi0m3_cs0: spi0m3-cs0 {
            rockchip,pins =
                /* SPI0_CS0_M3: GPIO3_C4, 功能号4 */
                <3 RK_PC4 4 &pcfg_pull_up>;
        };
    };
};

&spi0 {
    // 启用SPI0控制器
    status = "okay";
    
    // 应用修正后的引脚配置
    pinctrl-names = "default";
    pinctrl-0 = <&spi0m3_pins>, <&spi0m3_cs0>;
    
    // SPI控制器配置
    spi-max-frequency = <50000000>; // 50 MHz
    
    // SPI设备节点 - 使用推荐的兼容字符串
    spidev@0 {
        compatible = "rohm,dh2228fv";  // 推荐的兼容字符串，避免内核警告
        reg = <0>;                     // 使用CS0
        spi-max-frequency = <50000000>; // 50 MHz
    };
};
