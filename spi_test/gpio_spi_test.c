#include <stdio.h>
#include <stdlib.h>
#include <unistd.h>
#include <fcntl.h>
#include <sys/ioctl.h>
#include <linux/spi/spidev.h>
#include <stdint.h>
#include <string.h>
#include <sys/mman.h>

#define SPI_DEVICE "/dev/spidev0.0"

// RK3588 GPIO3 基地址
#define GPIO3_BASE 0xFEC40000
#define GPIO_SWPORT_DR_OFFSET 0x0000
#define GPIO_SWPORT_DDR_OFFSET 0x0004
#define GPIO_EXT_PORT_OFFSET 0x0070

// SPI0M3 引脚定义 (GPIO3)
#define SPI0_CLK_PIN  25  // GPIO3_C1
#define SPI0_MISO_PIN 26  // GPIO3_C2  
#define SPI0_MOSI_PIN 27  // GPIO3_C3
#define SPI0_CS0_PIN  28  // GPIO3_C4

void gpio_export(int pin) {
    int fd;
    char buffer[64];
    
    fd = open("/sys/class/gpio/export", O_WRONLY);
    if (fd < 0) {
        perror("无法打开gpio export");
        return;
    }
    
    snprintf(buffer, sizeof(buffer), "%d", pin);
    write(fd, buffer, strlen(buffer));
    close(fd);
}

void gpio_set_direction(int pin, const char* direction) {
    int fd;
    char path[64];
    
    snprintf(path, sizeof(path), "/sys/class/gpio/gpio%d/direction", pin);
    fd = open(path, O_WRONLY);
    if (fd < 0) {
        perror("无法设置GPIO方向");
        return;
    }
    
    write(fd, direction, strlen(direction));
    close(fd);
}

void gpio_set_value(int pin, int value) {
    int fd;
    char path[64];
    char val_str[2];
    
    snprintf(path, sizeof(path), "/sys/class/gpio/gpio%d/value", pin);
    fd = open(path, O_WRONLY);
    if (fd < 0) {
        perror("无法设置GPIO值");
        return;
    }
    
    snprintf(val_str, sizeof(val_str), "%d", value);
    write(fd, val_str, 1);
    close(fd);
}

int gpio_get_value(int pin) {
    int fd;
    char path[64];
    char value_str[3];
    
    snprintf(path, sizeof(path), "/sys/class/gpio/gpio%d/value", pin);
    fd = open(path, O_RDONLY);
    if (fd < 0) {
        perror("无法读取GPIO值");
        return -1;
    }
    
    if (read(fd, value_str, 3) < 0) {
        perror("读取GPIO值失败");
        close(fd);
        return -1;
    }
    
    close(fd);
    return atoi(value_str);
}

void manual_spi_transfer(uint8_t data) {
    int i;
    
    printf("手动SPI传输: 0x%02X\n", data);
    
    // CS拉低
    gpio_set_value(96 + SPI0_CS0_PIN, 0);
    usleep(1);
    
    // 发送8位数据
    for (i = 7; i >= 0; i--) {
        // 设置数据线
        gpio_set_value(96 + SPI0_MOSI_PIN, (data >> i) & 1);
        usleep(1);
        
        // 时钟上升沿
        gpio_set_value(96 + SPI0_CLK_PIN, 1);
        usleep(1);
        
        // 读取MISO
        int miso = gpio_get_value(96 + SPI0_MISO_PIN);
        printf("位%d: MOSI=%d, MISO=%d\n", i, (data >> i) & 1, miso);
        
        // 时钟下降沿
        gpio_set_value(96 + SPI0_CLK_PIN, 0);
        usleep(1);
    }
    
    // CS拉高
    gpio_set_value(96 + SPI0_CS0_PIN, 1);
    usleep(10);
}

int main() {
    printf("=== RK3588 GPIO SPI测试程序 ===\n");
    
    // 导出GPIO引脚
    printf("导出GPIO引脚...\n");
    gpio_export(96 + SPI0_CLK_PIN);   // GPIO3_25
    gpio_export(96 + SPI0_MISO_PIN);  // GPIO3_26
    gpio_export(96 + SPI0_MOSI_PIN);  // GPIO3_27
    gpio_export(96 + SPI0_CS0_PIN);   // GPIO3_28
    
    sleep(1); // 等待GPIO导出完成
    
    // 设置GPIO方向
    printf("配置GPIO方向...\n");
    gpio_set_direction(96 + SPI0_CLK_PIN, "out");
    gpio_set_direction(96 + SPI0_MISO_PIN, "in");
    gpio_set_direction(96 + SPI0_MOSI_PIN, "out");
    gpio_set_direction(96 + SPI0_CS0_PIN, "out");
    
    // 初始化GPIO状态
    printf("初始化GPIO状态...\n");
    gpio_set_value(96 + SPI0_CLK_PIN, 0);   // CLK低电平
    gpio_set_value(96 + SPI0_MOSI_PIN, 0);  // MOSI低电平
    gpio_set_value(96 + SPI0_CS0_PIN, 1);   // CS高电平(空闲)
    
    printf("GPIO配置完成!\n");
    printf("CLK: GPIO%d\n", 96 + SPI0_CLK_PIN);
    printf("MISO: GPIO%d\n", 96 + SPI0_MISO_PIN);
    printf("MOSI: GPIO%d\n", 96 + SPI0_MOSI_PIN);
    printf("CS0: GPIO%d\n", 96 + SPI0_CS0_PIN);
    
    // 测试GPIO输出
    printf("\n开始GPIO SPI测试...\n");
    
    // 发送测试数据
    uint8_t test_data[] = {0x55, 0xAA, 0xFF, 0x00, 0x01, 0x02, 0x03, 0x04};
    
    for (int i = 0; i < sizeof(test_data); i++) {
        printf("\n--- 测试数据 %d ---\n", i + 1);
        manual_spi_transfer(test_data[i]);
    }
    
    printf("\n测试完成!\n");
    printf("如果连接了SPI从设备，应该能在MISO线上看到响应数据\n");
    printf("如果没有连接设备，MISO应该保持高阻态或默认电平\n");
    
    return 0;
}
