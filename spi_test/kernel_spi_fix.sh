#!/bin/bash

echo "=== RK3588 SPI内核级修复脚本 ==="
echo ""

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}1. 分析当前问题${NC}"
echo "问题: SPI CLK/MISO/MOSI引脚未被SPI驱动正确控制"
echo "原因: pinctrl配置不完整，引脚未正确复用到SPI功能"

echo ""
echo -e "${BLUE}2. 直接操作pinctrl寄存器${NC}"

# RK3588 GPIO3 pinctrl寄存器地址
# GPIO3C1_SEL (CLK)  - 0xFD5F4020, bits [7:4] = 0100 (func 4)
# GPIO3C2_SEL (MISO) - 0xFD5F4020, bits [11:8] = 0100 (func 4)  
# GPIO3C3_SEL (MOSI) - 0xFD5F4020, bits [15:12] = 0100 (func 4)
# GPIO3C4_SEL (CS0)  - 0xFD5F4020, bits [19:16] = 0100 (func 4)

echo "尝试直接配置pinctrl寄存器..."

# 检查是否有devmem工具
if command -v devmem &> /dev/null; then
    echo "使用devmem配置寄存器..."
    
    # 读取当前寄存器值
    current_val=$(devmem 0xFD5F4020 32)
    echo "当前GPIO3C寄存器值: $current_val"
    
    # 计算新值 (设置GPIO3C1-C4为功能4)
    # 原值保持不变，只修改相关位
    # GPIO3C1[7:4]=4, GPIO3C2[11:8]=4, GPIO3C3[15:12]=4, GPIO3C4[19:16]=4
    new_val=0x44440000
    mask=0xFFFF0000
    
    echo "写入新的寄存器值..."
    devmem 0xFD5F4020 32 $new_val
    
    # 验证写入
    verify_val=$(devmem 0xFD5F4020 32)
    echo "验证寄存器值: $verify_val"
    
else
    echo -e "${YELLOW}devmem工具不可用，尝试其他方法...${NC}"
fi

echo ""
echo -e "${BLUE}3. 强制重新加载SPI驱动${NC}"

# 卸载并重新加载SPI驱动
echo "卸载SPI驱动..."
modprobe -r spidev 2>/dev/null || true
modprobe -r spi_rockchip 2>/dev/null || true

sleep 1

echo "重新加载SPI驱动..."
modprobe spi_rockchip
modprobe spidev

sleep 2

echo ""
echo -e "${BLUE}4. 强制绑定SPI设备${NC}"

# 强制重新绑定SPI控制器
echo "重新绑定SPI控制器..."
echo "feb00000.spi" > /sys/bus/platform/drivers/rockchip-spi/unbind 2>/dev/null || true
sleep 1
echo "feb00000.spi" > /sys/bus/platform/drivers/rockchip-spi/bind 2>/dev/null || true
sleep 2

echo ""
echo -e "${BLUE}5. 验证修复结果${NC}"

echo "检查SPI设备:"
ls -la /dev/spidev* 2>/dev/null || echo "SPI设备不存在"

echo ""
echo "检查pinctrl状态:"
cat /sys/kernel/debug/pinctrl/pinctrl-rockchip-pinctrl/pinmux-pins | grep -E "pin (121|122|123|124)"

echo ""
echo "检查GPIO状态:"
cat /sys/kernel/debug/gpio | grep -A15 "gpiochip3:" | grep -E "gpio-(121|122|123|124)"

echo ""
echo -e "${BLUE}6. 运行SPI测试${NC}"

# 创建并运行一个简单的SPI测试
cat > /tmp/spi_verify.c << 'EOF'
#include <stdio.h>
#include <stdlib.h>
#include <unistd.h>
#include <fcntl.h>
#include <sys/ioctl.h>
#include <linux/spi/spidev.h>
#include <stdint.h>

int main() {
    int fd;
    uint8_t mode = 0;
    uint8_t bits = 8;
    uint32_t speed = 1000000;
    uint8_t tx[] = {0x12, 0x34, 0x56, 0x78};
    uint8_t rx[4] = {0};
    
    printf("打开SPI设备...\n");
    fd = open("/dev/spidev0.0", O_RDWR);
    if (fd < 0) {
        perror("无法打开SPI设备");
        return -1;
    }
    
    printf("配置SPI参数...\n");
    ioctl(fd, SPI_IOC_WR_MODE, &mode);
    ioctl(fd, SPI_IOC_WR_BITS_PER_WORD, &bits);
    ioctl(fd, SPI_IOC_WR_MAX_SPEED_HZ, &speed);
    
    struct spi_ioc_transfer tr = {
        .tx_buf = (unsigned long)tx,
        .rx_buf = (unsigned long)rx,
        .len = 4,
        .speed_hz = speed,
        .bits_per_word = bits,
    };
    
    printf("执行SPI传输...\n");
    printf("发送: 0x%02X 0x%02X 0x%02X 0x%02X\n", tx[0], tx[1], tx[2], tx[3]);
    
    if (ioctl(fd, SPI_IOC_MESSAGE(1), &tr) < 1) {
        perror("SPI传输失败");
        close(fd);
        return -1;
    }
    
    printf("接收: 0x%02X 0x%02X 0x%02X 0x%02X\n", rx[0], rx[1], rx[2], rx[3]);
    printf("SPI传输完成!\n");
    
    close(fd);
    return 0;
}
EOF

echo "编译并运行SPI验证程序..."
gcc -o /tmp/spi_verify /tmp/spi_verify.c
/tmp/spi_verify

echo ""
echo -e "${BLUE}7. 生成最终报告${NC}"

# 检查修复是否成功
success=0
if cat /sys/kernel/debug/pinctrl/pinctrl-rockchip-pinctrl/pinmux-pins | grep -E "pin (121|122|123)" | grep -v "UNCLAIMED" | wc -l | grep -q "3"; then
    success=1
fi

if [ $success -eq 1 ]; then
    echo -e "${GREEN}✓ 修复成功! SPI引脚现在正确配置${NC}"
    echo "所有SPI信号线(CLK/MISO/MOSI/CS)现在应该都有输出"
else
    echo -e "${RED}✗ 修复未完全成功${NC}"
    echo "可能需要以下额外步骤:"
    echo "1. 修改设备树源文件"
    echo "2. 重新编译内核/设备树"
    echo "3. 更新bootloader配置"
    echo "4. 重启系统"
fi

echo ""
echo "清理临时文件..."
rm -f /tmp/spi_verify.c /tmp/spi_verify

echo ""
echo -e "${YELLOW}建议的验证步骤:${NC}"
echo "1. 使用示波器或逻辑分析仪观察SPI信号"
echo "2. 连接实际的SPI设备进行通信测试"
echo "3. 运行长时间的SPI传输测试"
