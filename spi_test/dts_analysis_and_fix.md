# RK3588 设备树SPI0配置分析和修正

## 🔍 发现的问题

### 1. **引脚复用功能号错误**
```dts
// 当前配置 (错误)
<3 RK_PD1 1 &pcfg_pull_none>,   // 功能号1是错误的
<3 RK_PD2 1 &pcfg_pull_none>,   
<3 RK_PD3 1 &pcfg_pull_none>,   
<3 RK_PD4 1 &pcfg_pull_up>;     

// 正确配置应该是
<3 RK_PD1 4 &pcfg_pull_none>,   // 功能号4才是SPI0功能
<3 RK_PD2 4 &pcfg_pull_none>,   
<3 RK_PD3 4 &pcfg_pull_none>,   
<3 RK_PD4 4 &pcfg_pull_up>;     
```

### 2. **引脚映射不匹配**
您使用的是GPIO3_D组引脚，但之前诊断显示实际使用的是GPIO3_C组：
- 诊断结果：GPIO3_C1-C4 (引脚121-124)
- 您的配置：GPIO3_D1-D4 (引脚125-128)

### 3. **兼容字符串问题**
```dts
compatible = "spidev";  // 不推荐，会产生警告
```

## 🛠️ 修正方案

### 方案1: 使用GPIO3_C组 (推荐)
```dts
&pinctrl {
    spi0 {
        spi0m3_pins: spi0m3-pins {
            rockchip,pins =
                // 使用 GPIO3_C 组引脚 (与诊断结果一致)
                <3 RK_PC1 4 &pcfg_pull_up>,     // SPI0_CLK_M3  (GPIO3_C1, pin 121)
                <3 RK_PC2 4 &pcfg_pull_up>,     // SPI0_MISO_M3 (GPIO3_C2, pin 122)
                <3 RK_PC3 4 &pcfg_pull_up>,     // SPI0_MOSI_M3 (GPIO3_C3, pin 123)
                <3 RK_PC4 4 &pcfg_pull_up>;     // SPI0_CS0_M3  (GPIO3_C4, pin 124)
        };
    };
};

&spi0 {
    status = "okay";
    pinctrl-names = "default";
    pinctrl-0 = <&spi0m3_pins>;
    
    spi-max-frequency = <50000000>;
    num-cs = <1>;
    
    // 使用推荐的兼容字符串
    spidev@0 {
        compatible = "rohm,dh2228fv";
        reg = <0>;
        spi-max-frequency = <50000000>;
    };
};
```

### 方案2: 继续使用GPIO3_D组 (需要验证硬件)
如果您的硬件确实使用GPIO3_D组，则修正功能号：
```dts
&pinctrl {
    spi0 {
        spi0m3_pins: spi0m3-pins {
            rockchip,pins =
                <3 RK_PD1 4 &pcfg_pull_up>,     // 功能号改为4
                <3 RK_PD2 4 &pcfg_pull_up>,     
                <3 RK_PD3 4 &pcfg_pull_up>,     
                <3 RK_PD4 4 &pcfg_pull_up>;     
        };
    };
};
```

## 📋 完整的修正版本

基于诊断结果，推荐使用以下配置：

```dts
&pinctrl {
    spi0 {
        spi0m3_pins: spi0m3-pins {
            rockchip,pins =
                /* SPI0_CLK_M3: GPIO3_C1 */
                <3 RK_PC1 4 &pcfg_pull_up_drv_level_2>,
                /* SPI0_MISO_M3: GPIO3_C2 */
                <3 RK_PC2 4 &pcfg_pull_up>,
                /* SPI0_MOSI_M3: GPIO3_C3 */
                <3 RK_PC3 4 &pcfg_pull_up_drv_level_2>;
        };
        
        spi0m3_cs0: spi0m3-cs0 {
            rockchip,pins =
                /* SPI0_CS0_M3: GPIO3_C4 */
                <3 RK_PC4 4 &pcfg_pull_up>;
        };
    };
};

&spi0 {
    status = "okay";
    pinctrl-names = "default";
    pinctrl-0 = <&spi0m3_pins>, <&spi0m3_cs0>;
    
    spi-max-frequency = <50000000>;
    
    spidev@0 {
        compatible = "rohm,dh2228fv";
        reg = <0>;
        spi-max-frequency = <50000000>;
    };
};
```

## 🔧 验证步骤

修改后重新编译和应用设备树：

```bash
# 1. 编译设备树
dtc -I dts -O dtb -o your-board.dtb your-board.dts

# 2. 替换设备树文件
sudo cp your-board.dtb /boot/

# 3. 重启系统
sudo reboot

# 4. 验证配置
cat /sys/kernel/debug/pinctrl/pinctrl-rockchip-pinctrl/pinmux-pins | grep -E "pin (121|122|123|124)"
```

## ⚠️ 重要说明

1. **引脚冲突检查**: 确保GPIO3_C1-C4没有被其他设备使用
2. **硬件验证**: 确认您的板子实际使用的SPI引脚位置
3. **功能号**: RK3588中SPI0功能对应的复用功能号是4，不是1
4. **驱动强度**: CLK和MOSI建议使用更高的驱动强度

## 🎯 引脚对应关系

| 信号 | GPIO | RK定义 | 引脚号 | 功能号 |
|------|------|--------|--------|--------|
| CLK  | GPIO3_C1 | RK_PC1 | 121 | 4 |
| MISO | GPIO3_C2 | RK_PC2 | 122 | 4 |
| MOSI | GPIO3_C3 | RK_PC3 | 123 | 4 |
| CS0  | GPIO3_C4 | RK_PC4 | 124 | 4 |

修正这些问题后，SPI0应该能正常工作，所有信号线都会有输出。
