#!/bin/bash

echo "=== RK3588 SPI设备树修复应用脚本 ==="
echo ""

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 检查是否有设备树编译器
if ! command -v dtc &> /dev/null; then
    echo -e "${RED}错误: 未找到设备树编译器 (dtc)${NC}"
    echo "请安装: sudo apt-get install device-tree-compiler"
    exit 1
fi

echo -e "${BLUE}1. 备份当前设备树${NC}"

# 查找当前使用的设备树文件
current_dtb=$(find /boot -name "*.dtb" | head -1)
if [ -z "$current_dtb" ]; then
    echo -e "${YELLOW}警告: 未在/boot目录找到dtb文件${NC}"
    echo "尝试查找其他位置..."
    current_dtb=$(find /lib/firmware -name "*.dtb" 2>/dev/null | head -1)
fi

if [ -n "$current_dtb" ]; then
    echo "找到设备树文件: $current_dtb"
    backup_file="${current_dtb}.backup.$(date +%Y%m%d_%H%M%S)"
    cp "$current_dtb" "$backup_file"
    echo "备份到: $backup_file"
else
    echo -e "${YELLOW}警告: 未找到当前设备树文件${NC}"
fi

echo ""
echo -e "${BLUE}2. 编译设备树覆盖文件${NC}"

# 编译设备树覆盖文件
echo "编译 rk3588_spi0_fix.dts..."
dtc -@ -I dts -O dtb -o rk3588_spi0_fix.dtbo rk3588_spi0_fix.dts

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✓ 设备树覆盖文件编译成功${NC}"
else
    echo -e "${RED}✗ 设备树覆盖文件编译失败${NC}"
    exit 1
fi

echo ""
echo -e "${BLUE}3. 应用设备树覆盖${NC}"

# 检查是否支持设备树覆盖
if [ -d "/sys/kernel/config/device-tree/overlays" ]; then
    echo "系统支持动态设备树覆盖"
    
    # 创建覆盖目录
    overlay_dir="/sys/kernel/config/device-tree/overlays/spi0_fix"
    if [ ! -d "$overlay_dir" ]; then
        mkdir -p "$overlay_dir"
    fi
    
    # 应用覆盖
    echo "应用设备树覆盖..."
    cat rk3588_spi0_fix.dtbo > "$overlay_dir/dtbo"
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✓ 设备树覆盖应用成功${NC}"
    else
        echo -e "${RED}✗ 设备树覆盖应用失败${NC}"
        echo "可能需要重启系统并手动替换设备树文件"
    fi
else
    echo -e "${YELLOW}系统不支持动态设备树覆盖${NC}"
    echo "需要手动替换设备树文件并重启"
fi

echo ""
echo -e "${BLUE}4. 验证修复效果${NC}"

sleep 2

echo "检查pinctrl状态:"
cat /sys/kernel/debug/pinctrl/pinctrl-rockchip-pinctrl/pinmux-pins | grep -E "pin (121|122|123|124)"

echo ""
echo "检查GPIO状态:"
cat /sys/kernel/debug/gpio | grep -A10 "gpiochip3:" | grep -E "gpio-(121|122|123|124)"

echo ""
echo -e "${BLUE}5. 测试SPI功能${NC}"

# 运行SPI测试
if [ -f "./simple_spi_test" ]; then
    echo "运行SPI测试程序..."
    timeout 5 ./simple_spi_test || true
else
    echo "SPI测试程序不存在，请先编译"
fi

echo ""
echo -e "${BLUE}6. 生成修复报告${NC}"

cat > spi_fix_report.txt << EOF
RK3588 SPI修复报告
生成时间: $(date)

修复前状态:
- CLK/MISO/MOSI引脚显示为"GPIO UNCLAIMED"
- 只有CS引脚正常工作

应用的修复:
- 重新定义了完整的pinctrl配置
- 修正了引脚复用设置
- 使用了正确的驱动强度配置

修复后状态:
$(cat /sys/kernel/debug/pinctrl/pinctrl-rockchip-pinctrl/pinmux-pins | grep -E "pin (121|122|123|124)")

GPIO状态:
$(cat /sys/kernel/debug/gpio | grep -A10 "gpiochip3:" | grep -E "gpio-(121|122|123|124)")

建议:
如果动态覆盖不生效，请重启系统。
如果问题仍然存在，可能需要修改主设备树文件。
EOF

echo "修复报告已保存到: spi_fix_report.txt"

echo ""
echo -e "${GREEN}修复脚本执行完成!${NC}"
echo ""
echo -e "${YELLOW}下一步建议:${NC}"
echo "1. 检查上述输出中的pinctrl状态"
echo "2. 如果引脚仍显示UNCLAIMED，请重启系统"
echo "3. 重启后运行SPI测试程序验证功能"
echo "4. 如果问题仍存在，可能需要修改主设备树源文件"
