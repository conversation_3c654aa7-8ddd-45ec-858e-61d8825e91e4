#!/bin/bash

echo "=== RK3588 SPI引脚配置修复脚本 ==="

# 检查当前SPI引脚状态
echo "1. 检查当前SPI0引脚配置:"
echo "GPIO3_25 (CLK): $(cat /sys/kernel/debug/pinctrl/pinctrl-rockchip-pinctrl/pinmux-pins | grep 'pin 121')"
echo "GPIO3_26 (MISO): $(cat /sys/kernel/debug/pinctrl/pinctrl-rockchip-pinctrl/pinmux-pins | grep 'pin 122')"
echo "GPIO3_27 (MOSI): $(cat /sys/kernel/debug/pinctrl/pinctrl-rockchip-pinctrl/pinmux-pins | grep 'pin 123')"
echo "GPIO3_28 (CS0): $(cat /sys/kernel/debug/pinctrl/pinctrl-rockchip-pinctrl/pinmux-pins | grep 'pin 124')"

echo ""
echo "2. 检查GPIO状态:"
cat /sys/kernel/debug/gpio | grep -A5 -B5 "gpio3:"

echo ""
echo "3. 问题分析:"
echo "- 从pinctrl信息看，只有CS引脚被正确配置"
echo "- CLK, MISO, MOSI引脚显示为'GPIO UNCLAIMED'"
echo "- 这说明设备树中的pinctrl配置可能不完整"

echo ""
echo "4. 建议的解决方案:"
echo "需要修改设备树文件，确保SPI0的pinctrl配置正确"

# 检查设备树源文件位置
echo ""
echo "5. 查找设备树文件:"
find /boot -name "*.dtb" 2>/dev/null | head -5
find /boot -name "*.dts" 2>/dev/null | head -5

echo ""
echo "6. 当前SPI设备状态:"
ls -la /dev/spidev*

echo ""
echo "7. SPI驱动加载状态:"
lsmod | grep spi

echo ""
echo "8. 内核消息中的SPI相关信息:"
dmesg | grep -i spi | tail -10
