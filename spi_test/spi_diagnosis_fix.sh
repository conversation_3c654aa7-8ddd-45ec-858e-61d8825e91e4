#!/bin/bash

echo "=== RK3588 SPI问题诊断和修复脚本 ==="
echo ""

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${YELLOW}1. 当前SPI设备状态检查${NC}"
echo "SPI设备文件:"
ls -la /dev/spidev* 2>/dev/null || echo "未找到SPI设备文件"

echo ""
echo -e "${YELLOW}2. SPI驱动加载状态${NC}"
echo "已加载的SPI相关模块:"
lsmod | grep spi || echo "未找到SPI模块"

echo ""
echo -e "${YELLOW}3. 内核消息中的SPI信息${NC}"
echo "SPI相关的内核消息:"
dmesg | grep -i spi | grep -E "(error|warning|fail)" || echo "未发现SPI错误消息"

echo ""
echo -e "${YELLOW}4. SPI引脚配置检查${NC}"
echo "SPI0 (feb00000.spi) 引脚配置:"
cat /sys/kernel/debug/pinctrl/pinctrl-rockchip-pinctrl/pinmux-pins | grep -E "pin (121|122|123|124)" | while read line; do
    if echo "$line" | grep -q "UNCLAIMED"; then
        echo -e "${RED}$line${NC}"
    else
        echo -e "${GREEN}$line${NC}"
    fi
done

echo ""
echo -e "${YELLOW}5. GPIO状态检查${NC}"
echo "GPIO3相关状态:"
cat /sys/kernel/debug/gpio | grep -A10 "gpiochip3:"

echo ""
echo -e "${YELLOW}6. 设备树配置检查${NC}"
echo "SPI0设备树状态:"
echo "Status: $(cat /sys/firmware/devicetree/base/spi@feb00000/status 2>/dev/null || echo '未找到')"
echo "Compatible: $(cat /sys/firmware/devicetree/base/spi@feb00000/mcu@0/compatible 2>/dev/null || echo '未找到')"

echo ""
echo -e "${YELLOW}7. 问题分析${NC}"
echo -e "${RED}发现的问题:${NC}"
echo "1. SPI CLK/MISO/MOSI引脚显示为'GPIO UNCLAIMED'"
echo "2. 只有CS引脚被正确配置"
echo "3. 设备树中使用了不推荐的'spidev'兼容字符串"

echo ""
echo -e "${YELLOW}8. 解决方案${NC}"
echo "需要修复设备树配置，确保所有SPI引脚都被正确配置。"

echo ""
echo -e "${YELLOW}9. 临时测试方案${NC}"
echo "尝试手动配置GPIO进行测试..."

# 尝试手动配置GPIO
echo "导出GPIO引脚..."
echo 121 > /sys/class/gpio/export 2>/dev/null
echo 122 > /sys/class/gpio/export 2>/dev/null  
echo 123 > /sys/class/gpio/export 2>/dev/null

sleep 1

echo "配置GPIO方向..."
echo "out" > /sys/class/gpio/gpio121/direction 2>/dev/null  # CLK
echo "in" > /sys/class/gpio/gpio122/direction 2>/dev/null   # MISO
echo "out" > /sys/class/gpio/gpio123/direction 2>/dev/null  # MOSI

echo "测试GPIO控制..."
echo "设置CLK为低电平..."
echo 0 > /sys/class/gpio/gpio121/value 2>/dev/null
echo "设置MOSI为低电平..."
echo 0 > /sys/class/gpio/gpio123/value 2>/dev/null

echo "读取当前GPIO状态:"
echo "CLK (GPIO121): $(cat /sys/class/gpio/gpio121/value 2>/dev/null || echo '无法读取')"
echo "MISO (GPIO122): $(cat /sys/class/gpio/gpio122/value 2>/dev/null || echo '无法读取')"
echo "MOSI (GPIO123): $(cat /sys/class/gpio/gpio123/value 2>/dev/null || echo '无法读取')"

echo ""
echo -e "${YELLOW}10. 测试GPIO翻转${NC}"
for i in {1..5}; do
    echo "测试 $i: 翻转CLK和MOSI"
    echo 1 > /sys/class/gpio/gpio121/value 2>/dev/null
    echo 1 > /sys/class/gpio/gpio123/value 2>/dev/null
    usleep 100000
    echo 0 > /sys/class/gpio/gpio121/value 2>/dev/null
    echo 0 > /sys/class/gpio/gpio123/value 2>/dev/null
    usleep 100000
    echo "CLK: $(cat /sys/class/gpio/gpio121/value 2>/dev/null), MOSI: $(cat /sys/class/gpio/gpio123/value 2>/dev/null), MISO: $(cat /sys/class/gpio/gpio122/value 2>/dev/null)"
done

echo ""
echo -e "${GREEN}诊断完成!${NC}"
echo ""
echo -e "${YELLOW}建议的修复步骤:${NC}"
echo "1. 修改设备树文件，正确配置SPI0的pinctrl"
echo "2. 重新编译设备树并更新"
echo "3. 重启系统验证修复效果"
echo ""
echo "如果GPIO能够正常翻转，说明硬件正常，问题在于设备树配置。"
