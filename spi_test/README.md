# RK3588 SPI主机测试程序

这个项目包含了两个用于测试RK3588 SPI主机功能的程序。

## 文件说明

- `spi_test.c` - 完整功能的SPI测试程序，支持多种参数配置
- `simple_spi_test.c` - 简化版SPI测试程序，易于理解和使用
- `Makefile` - 编译配置文件

## 编译方法

### 编译所有程序
```bash
make
```

### 单独编译简单版本
```bash
gcc -o simple_spi_test simple_spi_test.c
```

## 使用方法

### 1. 检查SPI设备
```bash
ls -la /dev/spidev*
```
应该能看到 `/dev/spidev0.0` 设备文件。

### 2. 运行简单测试程序
```bash
sudo ./simple_spi_test
```

### 3. 运行完整测试程序
```bash
# 使用默认参数
sudo ./spi_test

# 自定义参数
sudo ./spi_test -s 500000 -b 8 -d 10
```

## 程序功能

### simple_spi_test
- 基本的SPI通信测试
- 发送固定的测试数据
- 显示发送和接收的数据
- 循环测试模式，每500ms发送一次数据

### spi_test
- 支持多种SPI模式配置
- 可调节速度、位数、延时等参数
- 支持环回测试
- 更详细的配置选项

## 参数说明

spi_test程序支持以下参数：
- `-D` : 指定设备文件 (默认: /dev/spidev0.0)
- `-s` : 设置最大速度 (Hz)
- `-d` : 设置延时 (微秒)
- `-b` : 设置每字位数
- `-l` : 启用环回模式
- `-H` : 时钟相位
- `-O` : 时钟极性
- `-L` : 最低位优先
- `-C` : 片选高电平有效
- `-3` : 三线模式
- `-N` : 无片选
- `-R` : 使用就绪信号

## 测试结果说明

- **TX**: 发送的数据
- **RX**: 接收的数据

如果没有连接SPI从设备，接收数据通常为0x00。
如果连接了SPI从设备，会看到从设备返回的实际数据。

## 注意事项

1. 运行程序需要root权限 (使用sudo)
2. 确保SPI设备驱动已正确加载
3. 如果要连接实际的SPI设备，请注意电压匹配和接线正确性
4. 测试前请确认SPI引脚配置正确

## 故障排除

### 设备文件不存在
如果 `/dev/spidev0.0` 不存在，请检查：
1. 设备树配置是否正确
2. SPI驱动是否已加载
3. 内核配置是否启用了spidev

### 权限错误
确保使用sudo运行程序，或者将用户添加到相应的组中。

### 传输失败
检查SPI配置参数是否与硬件兼容，特别是速度和模式设置。
