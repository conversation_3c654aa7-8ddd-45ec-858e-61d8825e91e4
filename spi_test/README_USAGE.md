# RK3588 SPI测试工具使用说明

## 📂 文件结构

```
spi_test/
├── 测试程序
│   ├── simple_spi_test.c          # 基础SPI测试程序
│   ├── spi_signal_test.c          # 信号观察测试程序
│   ├── gpio_spi_test.c            # GPIO手动控制测试
│   └── spi_test.c                 # 完整功能SPI测试
├── 诊断工具
│   ├── deep_spi_analysis.sh       # 深度系统分析
│   ├── force_spi_fix.sh           # 强制修复尝试
│   ├── kernel_spi_fix.sh          # 内核级修复
│   └── spi_diagnosis_fix.sh       # 基础诊断修复
├── 设备树修复
│   ├── rk3588_spi0_fix.dts        # 设备树覆盖文件
│   ├── apply_devicetree_fix.sh    # 设备树应用脚本
│   └── devicetree_source_fix.md   # 详细修改指南
├── 文档
│   ├── FINAL_SOLUTION.md          # 最终解决方案
│   ├── SPI_DIAGNOSIS_REPORT.md    # 诊断报告
│   └── README_USAGE.md            # 本文件
└── Makefile                       # 编译配置
```

## 🚀 快速开始

### 1. 编译所有程序
```bash
cd spi_test
make
```

### 2. 运行基础诊断
```bash
sudo ./deep_spi_analysis.sh
```

### 3. 测试SPI功能
```bash
# 基础测试
sudo ./simple_spi_test

# 信号观察 (用于示波器)
sudo ./spi_signal_test
```

## 🔧 修复步骤

### 步骤1: 诊断问题
```bash
sudo ./deep_spi_analysis.sh
```
查看输出，确认CLK/MISO/MOSI引脚是否显示为"GPIO UNCLAIMED"

### 步骤2: 尝试快速修复
```bash
sudo ./force_spi_fix.sh
```

### 步骤3: 如果快速修复无效，尝试内核级修复
```bash
sudo ./kernel_spi_fix.sh
```

### 步骤4: 如果仍无效，应用设备树修复
```bash
sudo ./apply_devicetree_fix.sh
```

### 步骤5: 验证修复效果
```bash
# 检查pinctrl状态
cat /sys/kernel/debug/pinctrl/pinctrl-rockchip-pinctrl/pinmux-pins | grep -E "pin (121|122|123|124)"

# 运行测试
sudo ./simple_spi_test
```

## 📊 测试程序说明

### simple_spi_test
- **用途**: 基础SPI通信测试
- **特点**: 循环发送测试数据，显示发送和接收结果
- **运行**: `sudo ./simple_spi_test`

### spi_signal_test  
- **用途**: 专门用于示波器观察
- **特点**: 低速率(100KHz)，多种测试模式
- **运行**: `sudo ./spi_signal_test`

### gpio_spi_test
- **用途**: 手动GPIO控制，验证引脚硬件功能
- **特点**: 模拟SPI时序，逐位控制
- **运行**: `sudo ./gpio_spi_test`

### spi_test
- **用途**: 功能完整的SPI测试，支持多种参数
- **特点**: 命令行参数配置，专业级测试
- **运行**: `sudo ./spi_test -s 1000000 -b 8`

## 🔍 诊断工具说明

### deep_spi_analysis.sh
- **功能**: 全面分析SPI系统状态
- **输出**: 详细的系统信息和问题分析
- **用法**: `sudo ./deep_spi_analysis.sh`

### force_spi_fix.sh
- **功能**: 强制释放GPIO并重新绑定SPI驱动
- **适用**: GPIO被其他程序占用的情况
- **用法**: `sudo ./force_spi_fix.sh`

### kernel_spi_fix.sh
- **功能**: 内核级修复，包括寄存器操作和驱动重载
- **适用**: 深层次的配置问题
- **用法**: `sudo ./kernel_spi_fix.sh`

## 📋 常用命令

### 检查SPI状态
```bash
# 查看SPI设备
ls -la /dev/spidev*

# 查看pinctrl状态
cat /sys/kernel/debug/pinctrl/pinctrl-rockchip-pinctrl/pinmux-pins | grep -E "pin (121|122|123|124)"

# 查看GPIO状态
cat /sys/kernel/debug/gpio | grep -A10 "gpiochip3:"

# 查看SPI相关内核消息
dmesg | grep -i spi
```

### 手动GPIO控制
```bash
# 导出GPIO
echo 121 > /sys/class/gpio/export  # CLK
echo 122 > /sys/class/gpio/export  # MISO
echo 123 > /sys/class/gpio/export  # MOSI

# 设置方向
echo "out" > /sys/class/gpio/gpio121/direction
echo "in" > /sys/class/gpio/gpio122/direction
echo "out" > /sys/class/gpio/gpio123/direction

# 控制输出
echo 1 > /sys/class/gpio/gpio121/value
echo 0 > /sys/class/gpio/gpio121/value
```

## ⚠️ 注意事项

1. **权限**: 所有操作需要root权限 (使用sudo)
2. **备份**: 修改系统文件前请备份
3. **重启**: 某些修复可能需要重启系统
4. **硬件**: 确保SPI引脚没有被其他设备占用

## 🎯 预期结果

修复成功后，应该看到：

1. **pinctrl状态**: 所有SPI引脚都不再显示"UNCLAIMED"
2. **GPIO状态**: 引脚显示为SPI功能而不是GPIO
3. **信号输出**: 示波器上能观察到CLK、MOSI信号
4. **通信正常**: SPI测试程序能正常传输数据

## 🆘 故障排除

### 问题: 权限被拒绝
**解决**: 使用sudo运行所有命令

### 问题: 设备文件不存在
**解决**: 检查SPI驱动是否正确加载

### 问题: 修复后仍无信号输出
**解决**: 
1. 重启系统
2. 检查设备树配置
3. 联系硬件厂商

### 问题: 编译失败
**解决**: 
```bash
sudo apt-get update
sudo apt-get install build-essential
```

## 📞 获取帮助

如果遇到问题，请：

1. 运行完整诊断: `sudo ./deep_spi_analysis.sh > diagnosis.log`
2. 查看详细文档: `FINAL_SOLUTION.md`
3. 检查硬件连接和版本兼容性

## 📝 版本信息

- **适用平台**: RK3588
- **测试内核**: 5.10.160-rt89
- **SPI设备**: /dev/spidev0.0
- **引脚**: GPIO3_C1-C4 (121-124)
