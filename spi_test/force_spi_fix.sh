#!/bin/bash

echo "=== RK3588 SPI强制修复脚本 ==="
echo ""

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${RED}发现的问题:${NC}"
echo "GPIO 121-123被sysfs占用，而不是SPI驱动"
echo "这阻止了SPI信号的正常输出"
echo ""

echo -e "${BLUE}1. 释放被占用的GPIO${NC}"

# 释放被sysfs占用的GPIO
for gpio in 121 122 123; do
    if [ -d "/sys/class/gpio/gpio$gpio" ]; then
        echo "释放GPIO $gpio..."
        echo $gpio > /sys/class/gpio/unexport 2>/dev/null || true
        sleep 0.1
    fi
done

echo ""
echo -e "${BLUE}2. 重新初始化SPI驱动${NC}"

# 尝试重新绑定SPI驱动
echo "重新绑定SPI驱动..."
echo "feb00000.spi" > /sys/bus/platform/drivers/rockchip-spi/unbind 2>/dev/null || true
sleep 1
echo "feb00000.spi" > /sys/bus/platform/drivers/rockchip-spi/bind 2>/dev/null || true
sleep 1

echo ""
echo -e "${BLUE}3. 检查修复结果${NC}"

echo "检查GPIO状态:"
cat /sys/kernel/debug/gpio | grep -A5 -B5 "gpio-121\|gpio-122\|gpio-123\|gpio-124"

echo ""
echo "检查pinctrl状态:"
cat /sys/kernel/debug/pinctrl/pinctrl-rockchip-pinctrl/pinmux-pins | grep -E "pin (121|122|123|124)"

echo ""
echo -e "${BLUE}4. 测试SPI功能${NC}"

# 创建一个简单的SPI测试
cat > /tmp/spi_quick_test.c << 'EOF'
#include <stdio.h>
#include <stdlib.h>
#include <unistd.h>
#include <fcntl.h>
#include <sys/ioctl.h>
#include <linux/spi/spidev.h>
#include <stdint.h>

int main() {
    int fd;
    uint8_t mode = 0;
    uint8_t bits = 8;
    uint32_t speed = 1000000;
    uint8_t tx[] = {0xAA, 0x55};
    uint8_t rx[2] = {0};
    
    fd = open("/dev/spidev0.0", O_RDWR);
    if (fd < 0) {
        perror("无法打开SPI设备");
        return -1;
    }
    
    ioctl(fd, SPI_IOC_WR_MODE, &mode);
    ioctl(fd, SPI_IOC_WR_BITS_PER_WORD, &bits);
    ioctl(fd, SPI_IOC_WR_MAX_SPEED_HZ, &speed);
    
    struct spi_ioc_transfer tr = {
        .tx_buf = (unsigned long)tx,
        .rx_buf = (unsigned long)rx,
        .len = 2,
        .speed_hz = speed,
        .bits_per_word = bits,
    };
    
    printf("发送测试数据: 0x%02X 0x%02X\n", tx[0], tx[1]);
    
    if (ioctl(fd, SPI_IOC_MESSAGE(1), &tr) < 1) {
        perror("SPI传输失败");
        close(fd);
        return -1;
    }
    
    printf("接收数据: 0x%02X 0x%02X\n", rx[0], rx[1]);
    printf("SPI传输成功!\n");
    
    close(fd);
    return 0;
}
EOF

echo "编译并运行SPI测试..."
gcc -o /tmp/spi_quick_test /tmp/spi_quick_test.c
/tmp/spi_quick_test

echo ""
echo -e "${BLUE}5. 最终状态检查${NC}"

echo "最终GPIO状态:"
cat /sys/kernel/debug/gpio | grep -A10 "gpiochip3:" | grep -E "gpio-(121|122|123|124)"

echo ""
if cat /sys/kernel/debug/gpio | grep -E "gpio-(121|122|123)" | grep -q "spi0"; then
    echo -e "${GREEN}✓ 修复成功! SPI引脚现在由SPI驱动控制${NC}"
else
    echo -e "${RED}✗ 修复失败，需要更深层的设备树修改${NC}"
    echo ""
    echo -e "${YELLOW}建议的下一步:${NC}"
    echo "1. 修改设备树源文件"
    echo "2. 重新编译设备树"
    echo "3. 重启系统"
fi

echo ""
echo "清理临时文件..."
rm -f /tmp/spi_quick_test.c /tmp/spi_quick_test
