/*
 * RK3588 SPI0 完整修复设备树覆盖文件
 * 
 * 问题分析：
 * 1. SPI0的CLK/MISO/MOSI引脚显示为"GPIO UNCLAIMED"
 * 2. 只有CS引脚被正确配置
 * 3. pinctrl配置不完整或不正确
 * 
 * 解决方案：
 * 1. 重新定义完整的pinctrl配置
 * 2. 确保所有SPI引脚都被正确映射
 * 3. 使用正确的引脚复用功能
 */

/dts-v1/;
/plugin/;

/ {
    compatible = "rockchip,rk3588";
    
    fragment@0 {
        target-path = "/";
        __overlay__ {
            aliases {
                spi0 = "/spi@feb00000";
            };
        };
    };
    
    fragment@1 {
        target = <&pinctrl>;
        __overlay__ {
            spi0 {
                /delete-node/ spi0m3-pins;
                /delete-node/ spi0m3-cs0;
                
                spi0m3_pins: spi0m3-pins {
                    rockchip,pins =
                        /* spi0_clk_m3: GPIO3_C1 */
                        <3 25 4 &pcfg_pull_up_drv_level_2>,
                        /* spi0_miso_m3: GPIO3_C2 */
                        <3 26 4 &pcfg_pull_up>,
                        /* spi0_mosi_m3: GPIO3_C3 */
                        <3 27 4 &pcfg_pull_up_drv_level_2>;
                };
                
                spi0m3_cs0: spi0m3-cs0 {
                    rockchip,pins =
                        /* spi0_cs0_m3: GPIO3_C4 */
                        <3 28 4 &pcfg_pull_up>;
                };
                
                spi0m3_cs1: spi0m3-cs1 {
                    rockchip,pins =
                        /* spi0_cs1_m3: GPIO3_C5 */
                        <3 29 4 &pcfg_pull_up>;
                };
            };
        };
    };
    
    fragment@2 {
        target = <&spi0>;
        __overlay__ {
            status = "okay";
            pinctrl-names = "default";
            pinctrl-0 = <&spi0m3_pins>, <&spi0m3_cs0>;
            
            /* 删除原有的子节点 */
            /delete-node/ mcu@0;
            
            /* 重新定义spidev节点 */
            spidev@0 {
                compatible = "rohm,dh2228fv";
                reg = <0>;
                spi-max-frequency = <50000000>;
                spi-cpha;
                spi-cpol;
            };
        };
    };
};
