/*
 * RK3588 SPI0 设备树修复文件
 * 
 * 问题分析：
 * 1. 当前设备树中SPI0的pinctrl配置不完整，导致CLK/MISO/MOSI引脚未被正确配置
 * 2. 使用了不推荐的"spidev"兼容字符串
 * 
 * 解决方案：
 * 1. 修正pinctrl配置，确保所有SPI引脚都被正确配置
 * 2. 使用推荐的兼容字符串
 */

/dts-v1/;
/plugin/;

/ {
    compatible = "rockchip,rk3588";
    
    fragment@0 {
        target = <&spi0>;
        __overlay__ {
            status = "okay";
            pinctrl-names = "default";
            pinctrl-0 = <&spi0m3_pins>, <&spi0m3_cs0>;
            
            /* 删除原有的mcu@0节点，重新定义 */
            /delete-node/ mcu@0;
            
            spidev@0 {
                compatible = "rohm,dh2228fv";  /* 使用推荐的兼容字符串 */
                reg = <0>;
                spi-max-frequency = <10000000>;
            };
        };
    };
    
    fragment@1 {
        target = <&pinctrl>;
        __overlay__ {
            spi0 {
                spi0m3_pins: spi0m3-pins {
                    rockchip,pins =
                        /* spi0_clk_m3 */
                        <3 RK_PD1 4 &pcfg_pull_up>,
                        /* spi0_miso_m3 */
                        <3 RK_PD2 4 &pcfg_pull_up>,
                        /* spi0_mosi_m3 */
                        <3 RK_PD3 4 &pcfg_pull_up>;
                };
                
                spi0m3_cs0: spi0m3-cs0 {
                    rockchip,pins =
                        /* spi0_cs0_m3 */
                        <3 RK_PD4 4 &pcfg_pull_up>;
                };
            };
        };
    };
};
