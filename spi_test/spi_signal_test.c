#include <stdio.h>
#include <stdlib.h>
#include <unistd.h>
#include <fcntl.h>
#include <sys/ioctl.h>
#include <linux/spi/spidev.h>
#include <stdint.h>
#include <string.h>
#include <signal.h>

#define SPI_DEVICE "/dev/spidev0.0"

volatile int running = 1;

void signal_handler(int sig) {
    running = 0;
    printf("\n程序退出...\n");
}

int main() {
    int fd;
    int ret;
    uint8_t mode = SPI_MODE_0;
    uint8_t bits = 8;
    uint32_t speed = 100000; // 降低到100KHz便于观察
    uint16_t delay = 0;
    
    printf("=== RK3588 SPI信号测试程序 ===\n");
    printf("设备: %s\n", SPI_DEVICE);
    printf("速度: %d Hz (便于示波器观察)\n", speed);
    printf("按Ctrl+C退出\n\n");
    
    // 注册信号处理函数
    signal(SIGINT, signal_handler);
    
    // 打开SPI设备
    fd = open(SPI_DEVICE, O_RDWR);
    if (fd < 0) {
        perror("无法打开SPI设备");
        return -1;
    }
    
    // 配置SPI
    ret = ioctl(fd, SPI_IOC_WR_MODE, &mode);
    if (ret == -1) {
        perror("无法设置SPI模式");
        close(fd);
        return -1;
    }
    
    ret = ioctl(fd, SPI_IOC_WR_BITS_PER_WORD, &bits);
    if (ret == -1) {
        perror("无法设置位数");
        close(fd);
        return -1;
    }
    
    ret = ioctl(fd, SPI_IOC_WR_MAX_SPEED_HZ, &speed);
    if (ret == -1) {
        perror("无法设置速度");
        close(fd);
        return -1;
    }
    
    printf("SPI配置完成\n");
    printf("现在可以用示波器或逻辑分析仪观察以下引脚:\n");
    printf("- CLK:  GPIO3_25 (引脚121)\n");
    printf("- MISO: GPIO3_26 (引脚122)\n");
    printf("- MOSI: GPIO3_27 (引脚123)\n");
    printf("- CS0:  GPIO3_28 (引脚124)\n\n");
    
    // 测试数据模式
    uint8_t test_patterns[][8] = {
        {0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00}, // 全0
        {0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF}, // 全1
        {0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA}, // 10101010
        {0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55}, // 01010101
        {0x01, 0x02, 0x04, 0x08, 0x10, 0x20, 0x40, 0x80}, // 移位模式
        {0x80, 0x40, 0x20, 0x10, 0x08, 0x04, 0x02, 0x01}, // 反向移位
    };
    
    int pattern_count = sizeof(test_patterns) / sizeof(test_patterns[0]);
    int current_pattern = 0;
    int cycle = 0;
    
    while (running) {
        uint8_t *tx_data = test_patterns[current_pattern];
        uint8_t rx_data[8] = {0};
        
        struct spi_ioc_transfer tr = {
            .tx_buf = (unsigned long)tx_data,
            .rx_buf = (unsigned long)rx_data,
            .len = 8,
            .delay_usecs = delay,
            .speed_hz = speed,
            .bits_per_word = bits,
        };
        
        // 执行SPI传输
        ret = ioctl(fd, SPI_IOC_MESSAGE(1), &tr);
        if (ret < 1) {
            perror("SPI传输失败");
            break;
        }
        
        printf("周期 %d, 模式 %d: ", cycle, current_pattern);
        for (int i = 0; i < 8; i++) {
            printf("%02X ", tx_data[i]);
        }
        printf("\n");
        
        // 切换到下一个模式
        current_pattern = (current_pattern + 1) % pattern_count;
        if (current_pattern == 0) {
            cycle++;
            printf("--- 完成一轮测试，开始下一轮 ---\n");
        }
        
        usleep(500000); // 500ms延时
    }
    
    close(fd);
    printf("测试完成!\n");
    
    printf("\n如果SPI信号正常，您应该能在示波器上看到:\n");
    printf("1. CLK引脚有时钟信号输出\n");
    printf("2. MOSI引脚有数据信号输出\n");
    printf("3. CS引脚在传输时拉低\n");
    printf("4. 不同测试模式产生不同的波形\n");
    
    return 0;
}
