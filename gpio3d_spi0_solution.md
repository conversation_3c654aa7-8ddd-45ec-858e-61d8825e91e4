# GPIO3_D1-D4 复用为SPI0_M3 解决方案

## 🔍 当前状况分析

### 现有配置
- **当前spi0m3-pins**: 使用GPIO3_C组 (121-124)
- **目标**: 将GPIO3_D组 (125-128) 配置为SPI0功能
- **问题**: GPIO3_D1-D3被GPIO子系统占用，需要释放

### GPIO3_D组引脚映射
| 信号 | GPIO | 引脚号 | 当前状态 |
|------|------|--------|----------|
| SPI0_CLK  | GPIO3_D1 | 125 | gpio3:125 (被占用) |
| SPI0_MISO | GPIO3_D2 | 126 | gpio3:126 (被占用) |
| SPI0_MOSI | GPIO3_D3 | 127 | gpio3:127 (被占用) |
| SPI0_CS0  | GPIO3_D4 | 128 | 实际是GPIO4_A0，空闲 |

## 🛠️ 解决方案

### 步骤1: 释放被占用的GPIO

首先需要找到并禁用占用GPIO3_D1-D3的设备树节点：

```bash
# 查找占用这些GPIO的设备树节点
sudo find /sys/firmware/devicetree/base -name "*" -exec grep -l "gpio3.*29\|gpio3.*30\|gpio3.*31" {} \; 2>/dev/null

# 或者查找可能的GPIO配置
sudo grep -r "125\|126\|127" /sys/firmware/devicetree/base/ 2>/dev/null
```

### 步骤2: 修改设备树配置

#### 方案A: 重新定义spi0m3-pins (推荐)

```dts
&pinctrl {
    spi0 {
        // 删除原有的spi0m3-pins定义
        /delete-node/ spi0m3-pins;
        /delete-node/ spi0m3-cs0;
        
        // 重新定义使用GPIO3_D组
        spi0m3_pins: spi0m3-pins {
            rockchip,pins =
                /* SPI0_CLK: GPIO3_D1, 功能号需要确认 */
                <3 RK_PD1 4 &pcfg_pull_up_drv_level_2>,
                /* SPI0_MISO: GPIO3_D2, 功能号需要确认 */
                <3 RK_PD2 4 &pcfg_pull_up>,
                /* SPI0_MOSI: GPIO3_D3, 功能号需要确认 */
                <3 RK_PD3 4 &pcfg_pull_up_drv_level_2>;
        };
        
        spi0m3_cs0: spi0m3-cs0 {
            rockchip,pins =
                /* SPI0_CS0: GPIO4_A0 (原GPIO3_D4) */
                <4 RK_PA0 4 &pcfg_pull_up>;
        };
    };
};

&spi0 {
    status = "okay";
    pinctrl-names = "default";
    pinctrl-0 = <&spi0m3_pins>, <&spi0m3_cs0>;
    
    spidev@0 {
        compatible = "rohm,dh2228fv";
        reg = <0>;
        spi-max-frequency = <50000000>;
    };
};
```

#### 方案B: 创建新的SPI0复用组

```dts
&pinctrl {
    spi0 {
        // 保留原有配置，创建新的复用组
        spi0m4_pins: spi0m4-pins {
            rockchip,pins =
                <3 RK_PD1 4 &pcfg_pull_up_drv_level_2>,  // CLK
                <3 RK_PD2 4 &pcfg_pull_up>,              // MISO
                <3 RK_PD3 4 &pcfg_pull_up_drv_level_2>;  // MOSI
        };
        
        spi0m4_cs0: spi0m4-cs0 {
            rockchip,pins =
                <4 RK_PA0 4 &pcfg_pull_up>;              // CS0
        };
    };
};

&spi0 {
    status = "okay";
    pinctrl-names = "default";
    pinctrl-0 = <&spi0m4_pins>, <&spi0m4_cs0>;  // 使用新的复用组
    
    spidev@0 {
        compatible = "rohm,dh2228fv";
        reg = <0>;
        spi-max-frequency = <50000000>;
    };
};
```

### 步骤3: 确认正确的功能号

GPIO3_D组的SPI功能号可能不是4，需要确认：

```bash
# 方法1: 尝试不同的功能号 (1-7)
# 在设备树中尝试功能号1-7，看哪个能正确配置

# 方法2: 查看寄存器手册
# GPIO3D_IOMUX寄存器地址: 0xFD5F4024
# 每个引脚4位，查看SPI功能对应的值
```

### 步骤4: 释放GPIO占用的脚本

```bash
#!/bin/bash
# 强制释放GPIO3_D1-D3

echo "释放GPIO占用..."

# 尝试从GPIO子系统中释放
for pin in 125 126 127; do
    if [ -d "/sys/class/gpio/gpio$pin" ]; then
        echo $pin > /sys/class/gpio/unexport 2>/dev/null || true
    fi
done

# 重新绑定pinctrl
echo "重新绑定pinctrl..."
echo "pinctrl-rockchip-pinctrl" > /sys/bus/platform/drivers/pinctrl-rockchip/unbind 2>/dev/null || true
sleep 1
echo "pinctrl-rockchip-pinctrl" > /sys/bus/platform/drivers/pinctrl-rockchip/bind 2>/dev/null || true

# 重新绑定SPI驱动
echo "重新绑定SPI驱动..."
echo "feb00000.spi" > /sys/bus/platform/drivers/rockchip-spi/unbind 2>/dev/null || true
sleep 1
echo "feb00000.spi" > /sys/bus/platform/drivers/rockchip-spi/bind 2>/dev/null || true
```

## ⚠️ 注意事项

1. **功能号确认**: GPIO3_D组的SPI功能号可能不是4，需要查阅芯片手册或尝试
2. **GPIO4_A0**: GPIO3_D4实际上是GPIO4_A0，需要相应调整
3. **冲突检查**: 确保没有其他设备使用这些引脚
4. **备份**: 修改前备份原始设备树文件

## 🔧 验证步骤

修改后验证：

```bash
# 检查引脚配置
sudo cat /sys/kernel/debug/pinctrl/pinctrl-rockchip-pinctrl/pinmux-pins | grep -E "pin (125|126|127|128)"

# 检查SPI功能
sudo ./spi_test/simple_spi_test

# 检查GPIO状态
sudo cat /sys/kernel/debug/gpio | grep -A20 "gpiochip3:" | grep -E "gpio-(125|126|127)"
```

## 📝 总结

要将GPIO3_D1-D4复用为SPI0功能，需要：
1. 释放被GPIO子系统占用的引脚
2. 修改设备树pinctrl配置
3. 确认正确的功能复用号
4. 处理GPIO3_D4实际是GPIO4_A0的问题

建议先尝试方案A，如果功能号4不正确，再尝试其他功能号。
