#include <stdio.h>
#include <stdlib.h>
#include <unistd.h>
#include <fcntl.h>
#include <string.h>
#include <signal.h>
#include <sys/stat.h>

#define PWM_CHIP_PATH "/sys/class/pwm/pwmchip0"
#define PWM_CHANNEL "0"
#define PWM_PERIOD_NS 50000  // 50us = 20KHz
#define THERMAL_ZONE "/sys/class/thermal/thermal_zone0/temp"

volatile int running = 1;

void signal_handler(int sig) {
    running = 0;
    printf("\n程序退出...\n");
}

int file_exists(const char *path) {
    struct stat st;
    return (stat(path, &st) == 0);
}

int write_file(const char *path, const char *value) {
    int fd = open(path, O_WRONLY);
    if (fd < 0) {
        perror("打开文件失败");
        return -1;
    }
    
    int len = strlen(value);
    if (write(fd, value, len) != len) {
        perror("写入文件失败");
        close(fd);
        return -1;
    }
    
    close(fd);
    return 0;
}

int read_file(const char *path, char *buffer, int size) {
    int fd = open(path, O_RDONLY);
    if (fd < 0) {
        perror("打开文件失败");
        return -1;
    }
    
    int len = read(fd, buffer, size - 1);
    if (len < 0) {
        perror("读取文件失败");
        close(fd);
        return -1;
    }
    
    buffer[len] = '\0';
    close(fd);
    return len;
}

int init_pwm() {
    char path[256];
    char value[64];
    
    printf("初始化PWM...\n");
    
    snprintf(path, sizeof(path), "%s/pwm%s", PWM_CHIP_PATH, PWM_CHANNEL);
    if (!file_exists(path)) {
        snprintf(path, sizeof(path), "%s/export", PWM_CHIP_PATH);
        if (write_file(path, PWM_CHANNEL) < 0) {
            printf("导出PWM通道失败\n");
            return -1;
        }
        usleep(100000);
    }
    
    snprintf(path, sizeof(path), "%s/pwm%s/period", PWM_CHIP_PATH, PWM_CHANNEL);
    snprintf(value, sizeof(value), "%d", PWM_PERIOD_NS);
    if (write_file(path, value) < 0) {
        printf("设置PWM周期失败\n");
        return -1;
    }
    
    snprintf(path, sizeof(path), "%s/pwm%s/duty_cycle", PWM_CHIP_PATH, PWM_CHANNEL);
    if (write_file(path, "0") < 0) {
        printf("设置PWM占空比失败\n");
        return -1;
    }
    
    snprintf(path, sizeof(path), "%s/pwm%s/enable", PWM_CHIP_PATH, PWM_CHANNEL);
    if (write_file(path, "1") < 0) {
        printf("启用PWM失败\n");
        return -1;
    }
    
    printf("PWM初始化成功\n");
    return 0;
}

int set_fan_speed(int level) {
    char path[256];
    char value[64];
    int duty_cycle;
    
    duty_cycle = (PWM_PERIOD_NS * level) / 255;
    
    snprintf(path, sizeof(path), "%s/pwm%s/duty_cycle", PWM_CHIP_PATH, PWM_CHANNEL);
    snprintf(value, sizeof(value), "%d", duty_cycle);
    
    if (write_file(path, value) < 0) {
        printf("设置风扇速度失败\n");
        return -1;
    }
    
    printf("设置风扇速度: %d/255 (占空比: %d/%d ns)\n", level, duty_cycle, PWM_PERIOD_NS);
    return 0;
}

int get_temperature() {
    char buffer[64];
    int temp;
    
    if (read_file(THERMAL_ZONE, buffer, sizeof(buffer)) < 0) {
        return -1;
    }
    
    temp = atoi(buffer) / 1000;
    return temp;
}

void cleanup_pwm() {
    char path[256];
    
    printf("清理PWM资源...\n");
    
    snprintf(path, sizeof(path), "%s/pwm%s/enable", PWM_CHIP_PATH, PWM_CHANNEL);
    write_file(path, "0");
    
    snprintf(path, sizeof(path), "%s/unexport", PWM_CHIP_PATH);
    write_file(path, PWM_CHANNEL);
}

void print_usage(const char *prog) {
    printf("用法: %s [选项]\n", prog);
    printf("选项:\n");
    printf("  -a          自动模式 (根据温度自动调节)\n");
    printf("  -s <0-255>  手动设置风扇速度\n");
    printf("  -t          测试模式 (循环测试不同速度)\n");
    printf("  -h          显示帮助信息\n");
}

int main(int argc, char *argv[]) {
    int opt;
    int manual_speed = -1;
    int auto_mode = 0;
    int test_mode = 0;
    
    printf("=== RK3588 风扇控制测试程序 ===\n");
    
    signal(SIGINT, signal_handler);
    signal(SIGTERM, signal_handler);
    
    while ((opt = getopt(argc, argv, "as:th")) != -1) {
        switch (opt) {
            case 'a':
                auto_mode = 1;
                break;
            case 's':
                manual_speed = atoi(optarg);
                if (manual_speed < 0 || manual_speed > 255) {
                    printf("错误: 风扇速度必须在0-255之间\n");
                    return 1;
                }
                break;
            case 't':
                test_mode = 1;
                break;
            case 'h':
                print_usage(argv[0]);
                return 0;
            default:
                print_usage(argv[0]);
                return 1;
        }
    }
    
    if (init_pwm() < 0) {
        printf("PWM初始化失败\n");
        return 1;
    }
    
    if (test_mode) {
        printf("测试模式: 循环测试不同风扇速度\n");
        printf("按Ctrl+C退出\n\n");
        
        int speeds[] = {0, 50, 100, 150, 200, 255};
        int speed_count = sizeof(speeds) / sizeof(speeds[0]);
        int i = 0;
        
        while (running) {
            int temp = get_temperature();
            printf("温度: %d°C, ", temp > 0 ? temp : 0);
            set_fan_speed(speeds[i]);
            
            i = (i + 1) % speed_count;
            sleep(3);
        }
    } else if (auto_mode) {
        printf("自动模式: 根据温度自动调节风扇速度\n");
        printf("按Ctrl+C退出\n\n");
        
        while (running) {
            int temp = get_temperature();
            int fan_level = 0;
            
            if (temp >= 0) {
                if (temp < 50) fan_level = 0;
                else if (temp < 55) fan_level = 50;
                else if (temp < 60) fan_level = 100;
                else if (temp < 65) fan_level = 150;
                else if (temp < 70) fan_level = 200;
                else fan_level = 255;
                
                printf("温度: %d°C, 风扇等级: %d\n", temp, fan_level);
                set_fan_speed(fan_level);
            }
            sleep(2);
        }
    } else if (manual_speed >= 0) {
        printf("手动模式: 设置风扇速度为 %d\n", manual_speed);
        set_fan_speed(manual_speed);
        printf("风扇速度已设置，程序退出\n");
    } else {
        print_usage(argv[0]);
        cleanup_pwm();
        return 1;
    }
    
    set_fan_speed(0);
    cleanup_pwm();
    
    printf("程序结束\n");
    return 0;
}
