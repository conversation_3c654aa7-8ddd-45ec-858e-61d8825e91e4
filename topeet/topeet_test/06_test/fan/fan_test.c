#include <stdio.h>
#include <stdlib.h>
#include <unistd.h>
#include <fcntl.h>
#include <string.h>
#include <signal.h>

#define PWM_DUTY_CYCLE_PATH "/sys/class/pwm/pwmchip2/pwm0/duty_cycle"
#define PWM_ENABLE_PATH "/sys/class/pwm/pwmchip2/pwm0/enable"
#define THERMAL_ZONE "/sys/class/thermal/thermal_zone0/temp"
#define PWM_PERIOD_NS 50000

volatile int running = 1;

void signal_handler(int sig) {
    running = 0;
    printf("\n程序退出...\n");
}

int write_file(const char *path, const char *value) {
    int fd = open(path, O_WRONLY);
    if (fd < 0) {
        perror("打开文件失败");
        return -1;
    }
    
    int len = strlen(value);
    if (write(fd, value, len) != len) {
        perror("写入文件失败");
        close(fd);
        return -1;
    }
    
    close(fd);
    return 0;
}

int read_file(const char *path, char *buffer, int size) {
    int fd = open(path, O_RDONLY);
    if (fd < 0) {
        perror("打开文件失败");
        return -1;
    }
    
    int len = read(fd, buffer, size - 1);
    if (len < 0) {
        perror("读取文件失败");
        close(fd);
        return -1;
    }
    
    buffer[len] = '\0';
    close(fd);
    return len;
}

int set_fan_speed(int level) {
    char value[64];
    int duty_cycle;
    
    duty_cycle = (PWM_PERIOD_NS * level) / 255;
    snprintf(value, sizeof(value), "%d", duty_cycle);
    
    if (write_file(PWM_DUTY_CYCLE_PATH, value) < 0) {
        return -1;
    }
    
    return 0;
}

int get_temperature() {
    char buffer[64];
    int temp;
    
    if (read_file(THERMAL_ZONE, buffer, sizeof(buffer)) < 0) {
        return -1;
    }
    
    temp = atoi(buffer) / 1000;
    return temp;
}

int main() {
    printf("=== RK3588 风扇测试程序 ===\n");
    printf("按Ctrl+C退出\n\n");
    
    signal(SIGINT, signal_handler);
    signal(SIGTERM, signal_handler);
    
    // 启用PWM
    write_file(PWM_ENABLE_PATH, "1");
    
    int speeds[] = {0, 50, 100, 150, 200, 255};
    int speed_count = sizeof(speeds) / sizeof(speeds[0]);
    int i = 0;
    
    while (running) {
        int temp = get_temperature();
        int speed = speeds[i];
        
        printf("温度: %d°C, 风扇速度: %d/255 (%.1f%%)\n", 
               temp > 0 ? temp : 0, speed, (speed * 100.0) / 255);
        
        set_fan_speed(speed);
        
        i = (i + 1) % speed_count;
        sleep(3);
    }
    
    // 关闭风扇
    set_fan_speed(0);
    printf("风扇已关闭\n");
    
    return 0;
}
