#include <stdio.h>
#include <stdlib.h>
#include <unistd.h>
#include <fcntl.h>
#include <string.h>

#define PWM_DUTY_CYCLE_PATH "/sys/class/pwm/pwmchip2/pwm0/duty_cycle"
#define PWM_ENABLE_PATH "/sys/class/pwm/pwmchip2/pwm0/enable"
#define PWM_PERIOD_PATH "/sys/class/pwm/pwmchip2/pwm0/period"
#define THERMAL_ZONE "/sys/class/thermal/thermal_zone0/temp"
#define PWM_PERIOD_NS 50000

int write_file(const char *path, const char *value) {
    int fd = open(path, O_WRONLY);
    if (fd < 0) {
        perror("打开文件失败");
        return -1;
    }
    
    int len = strlen(value);
    if (write(fd, value, len) != len) {
        perror("写入文件失败");
        close(fd);
        return -1;
    }
    
    close(fd);
    return 0;
}

int read_file(const char *path, char *buffer, int size) {
    int fd = open(path, O_RDONLY);
    if (fd < 0) {
        perror("打开文件失败");
        return -1;
    }
    
    int len = read(fd, buffer, size - 1);
    if (len < 0) {
        perror("读取文件失败");
        close(fd);
        return -1;
    }
    
    buffer[len] = '\0';
    close(fd);
    return len;
}

int set_fan_speed(int level) {
    char value[64];
    int duty_cycle;
    
    // 将速度等级(0-255)转换为占空比
    duty_cycle = (PWM_PERIOD_NS * level) / 255;
    
    snprintf(value, sizeof(value), "%d", duty_cycle);
    
    if (write_file(PWM_DUTY_CYCLE_PATH, value) < 0) {
        printf("设置风扇速度失败\n");
        return -1;
    }
    
    printf("设置风扇速度: %d/255 (占空比: %d/%d ns)\n", level, duty_cycle, PWM_PERIOD_NS);
    return 0;
}

int get_temperature() {
    char buffer[64];
    int temp;
    
    if (read_file(THERMAL_ZONE, buffer, sizeof(buffer)) < 0) {
        return -1;
    }
    
    temp = atoi(buffer) / 1000;
    return temp;
}

int main(int argc, char *argv[]) {
    if (argc != 3 || strcmp(argv[1], "-s") != 0) {
        printf("用法: %s -s <0-255>\n", argv[0]);
        printf("示例: %s -s 100\n", argv[0]);
        return 1;
    }
    
    int speed = atoi(argv[2]);
    if (speed < 0 || speed > 255) {
        printf("错误: 风扇速度必须在0-255之间\n");
        return 1;
    }
    
    printf("=== 简单风扇控制程序 ===\n");
    
    // 确保PWM已启用
    printf("启用PWM...\n");
    if (write_file(PWM_ENABLE_PATH, "1") < 0) {
        printf("启用PWM失败，可能已经启用\n");
    }
    
    // 获取当前温度
    int temp = get_temperature();
    if (temp > 0) {
        printf("当前温度: %d°C\n", temp);
    }
    
    // 设置风扇速度
    if (set_fan_speed(speed) == 0) {
        printf("风扇速度设置成功!\n");
        printf("等待5秒后关闭风扇...\n");
        sleep(5);
        
        // 关闭风扇
        set_fan_speed(0);
        printf("风扇已关闭\n");
    }
    
    return 0;
}
