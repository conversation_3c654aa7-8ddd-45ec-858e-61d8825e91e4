# RK3588 风扇控制测试程序

## 概述

本目录包含了用于RK3588平台风扇控制的测试程序，基于PWM控制风扇转速。

## 硬件配置

根据设备树配置，风扇控制使用：
- **PWM控制器**: pwm12 (对应 /sys/class/pwm/pwmchip2)
- **PWM频率**: 20KHz (周期50000ns)
- **控制范围**: 0-255 (对应0%-100%占空比)

## 程序说明

### 1. simple_fan_control
**简单风扇控制程序**
- 用法: `sudo ./simple_fan_control -s <0-255>`
- 功能: 设置指定的风扇速度，运行5秒后自动关闭
- 示例: `sudo ./simple_fan_control -s 100`

### 2. fan_test
**风扇循环测试程序**
- 用法: `sudo ./fan_test`
- 功能: 循环测试不同风扇速度 (0, 50, 100, 150, 200, 255)
- 每个速度持续3秒，显示当前温度
- 按Ctrl+C退出

### 3. fan_control (原版)
**完整功能风扇控制程序**
- 支持多种模式但可能有兼容性问题
- 建议使用上述简化版本

## 编译方法

```bash
# 编译所有程序
gcc -Wall -O2 -o simple_fan_control simple_fan_control.c
gcc -Wall -O2 -o fan_test fan_test.c
gcc -Wall -O2 -o fan_control fan_control.c
```

## 使用示例

### 基本测试
```bash
# 设置风扇速度为50%
sudo ./simple_fan_control -s 128

# 设置风扇速度为100%
sudo ./simple_fan_control -s 255

# 关闭风扇
sudo ./simple_fan_control -s 0
```

### 循环测试
```bash
# 运行循环测试，观察风扇转速变化
sudo ./fan_test
```

## 系统要求

1. **PWM设备**: 确保 `/sys/class/pwm/pwmchip2` 存在
2. **权限**: 需要root权限操作PWM设备
3. **PWM导出**: 需要先导出PWM通道0

### 手动PWM设置

如果程序无法自动配置，可以手动设置：

```bash
# 导出PWM通道
echo 0 | sudo tee /sys/class/pwm/pwmchip2/export

# 设置周期 (20KHz)
echo 50000 | sudo tee /sys/class/pwm/pwmchip2/pwm0/period

# 设置占空比 (50%速度)
echo 25000 | sudo tee /sys/class/pwm/pwmchip2/pwm0/duty_cycle

# 启用PWM
echo 1 | sudo tee /sys/class/pwm/pwmchip2/pwm0/enable

# 关闭PWM
echo 0 | sudo tee /sys/class/pwm/pwmchip2/pwm0/enable

# 取消导出
echo 0 | sudo tee /sys/class/pwm/pwmchip2/unexport
```

## 温度监控

程序会读取 `/sys/class/thermal/thermal_zone0/temp` 获取CPU温度。

## 故障排除

### 1. PWM设备不存在
检查设备树配置，确保pwm12已启用。

### 2. 权限错误
确保使用sudo运行程序。

### 3. 设备忙碌
如果提示"Device or resource busy"，可能是：
- PWM通道已被其他程序占用
- 需要先取消导出再重新导出

### 4. 风扇不转
检查：
- 风扇电源连接
- PWM信号线连接
- 风扇是否支持PWM控制

## 测试结果

程序成功运行时会显示：
- 当前CPU温度
- 设置的风扇速度 (0-255)
- 对应的PWM占空比

## 注意事项

1. 风扇速度0表示完全关闭
2. 风扇速度255表示最大转速
3. 程序退出时会自动关闭风扇
4. 建议在有风扇连接的情况下测试
