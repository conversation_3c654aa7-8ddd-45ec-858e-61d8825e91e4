// GPIO3_D组 SPI0 设备树配置
// 将GPIO3_D1-D4配置为SPI0_M3功能

&pinctrl {
    spi0 {
        // 删除原有的spi0m3配置 (如果存在)
        /delete-node/ spi0m3-pins;
        /delete-node/ spi0m3-cs0;
        
        // 重新定义使用GPIO3_D组
        spi0m3_pins: spi0m3-pins {
            rockchip,pins =
                /* SPI0_CLK: GPIO3_D1 (pin 125) */
                <3 RK_PD1 4 &pcfg_pull_up_drv_level_2>,
                /* SPI0_MISO: GPIO3_D2 (pin 126) */
                <3 RK_PD2 4 &pcfg_pull_up>,
                /* SPI0_MOSI: GPIO3_D3 (pin 127) */
                <3 RK_PD3 4 &pcfg_pull_up_drv_level_2>;
        };
        
        spi0m3_cs0: spi0m3-cs0 {
            rockchip,pins =
                /* SPI0_CS0: GPIO4_A0 (pin 128, 注意是GPIO4) */
                <4 RK_PA0 4 &pcfg_pull_up>;
        };
    };
};

&spi0 {
    status = "okay";
    pinctrl-names = "default";
    pinctrl-0 = <&spi0m3_pins>, <&spi0m3_cs0>;
    
    spi-max-frequency = <50000000>;
    
    spidev@0 {
        compatible = "rohm,dh2228fv";
        reg = <0>;
        spi-max-frequency = <50000000>;
    };
};
