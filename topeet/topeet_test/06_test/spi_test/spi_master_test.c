#include <stdio.h>
#include <stdlib.h>
#include <unistd.h>
#include <fcntl.h>
#include <string.h>
#include <sys/ioctl.h>
#include <linux/types.h>
#include <linux/spi/spidev.h>
#include <stdint.h>
#include <errno.h>
#include <getopt.h>

#define SPI_DEVICE "/dev/spidev0.0"
#define ARRAY_SIZE(a) (sizeof(a) / sizeof((a)[0]))

static void pabort(const char *s)
{
    perror(s);
    abort();
}

static const char *device = SPI_DEVICE;
static uint32_t mode = SPI_MODE_0;
static uint8_t bits = 8;
static uint32_t speed = 1000000;  // 1MHz
static uint16_t delay = 0;

static void print_usage(const char *prog)
{
    printf("Usage: %s [-DsbdlHOLC3]\n", prog);
    printf("  -D --device   device to use (default %s)\n", device);
    printf("  -s --speed    max speed (Hz)\n");
    printf("  -d --delay    delay (usec)\n");
    printf("  -b --bpw      bits per word\n");
    printf("  -l --loop     loopback\n");
    printf("  -H --cpha     clock phase\n");
    printf("  -O --cpol     clock polarity\n");
    printf("  -L --lsb      least significant bit first\n");
    printf("  -C --cs-high  chip select active high\n");
    printf("  -3 --3wire    SI/SO signals shared\n");
    printf("  -v --verbose  Verbose (show tx buffer)\n");
    printf("  -p            Send data from stdin\n");
    printf("  -N --no-cs    no chip select\n");
    printf("  -R --ready    use ready signal\n");
}

static void parse_opts(int argc, char *argv[])
{
    while (1) {
        static const struct option lopts[] = {
            { "device",  1, 0, 'D' },
            { "speed",   1, 0, 's' },
            { "delay",   1, 0, 'd' },
            { "bpw",     1, 0, 'b' },
            { "loop",    0, 0, 'l' },
            { "cpha",    0, 0, 'H' },
            { "cpol",    0, 0, 'O' },
            { "lsb",     0, 0, 'L' },
            { "cs-high", 0, 0, 'C' },
            { "3wire",   0, 0, '3' },
            { "no-cs",   0, 0, 'N' },
            { "ready",   0, 0, 'R' },
            { "verbose", 0, 0, 'v' },
            { NULL, 0, 0, 0 },
        };
        int c;

        c = getopt_long(argc, argv, "D:s:d:b:lHOLC3NRvp",
                lopts, NULL);

        if (c == -1)
            break;

        switch (c) {
        case 'D':
            device = optarg;
            break;
        case 's':
            speed = atoi(optarg);
            break;
        case 'd':
            delay = atoi(optarg);
            break;
        case 'b':
            bits = atoi(optarg);
            break;
        case 'l':
            mode |= SPI_LOOP;
            break;
        case 'H':
            mode |= SPI_CPHA;
            break;
        case 'O':
            mode |= SPI_CPOL;
            break;
        case 'L':
            mode |= SPI_LSB_FIRST;
            break;
        case 'C':
            mode |= SPI_CS_HIGH;
            break;
        case '3':
            mode |= SPI_3WIRE;
            break;
        case 'N':
            mode |= SPI_NO_CS;
            break;
        case 'R':
            mode |= SPI_READY;
            break;
        case 'v':
            // verbose flag handled in main
            break;
        case 'p':
            // stdin flag handled in main
            break;
        default:
            print_usage(argv[0]);
            exit(1);
            break;
        }
    }
}

static void transfer(int fd, uint8_t const *tx, uint8_t const *rx, size_t len)
{
    int ret;
    int out_fd;
    struct spi_ioc_transfer tr = {
        .tx_buf = (unsigned long)tx,
        .rx_buf = (unsigned long)rx,
        .len = len,
        .delay_usecs = delay,
        .speed_hz = speed,
        .bits_per_word = bits,
    };

    if (mode & SPI_TX_QUAD)
        tr.tx_nbits = 4;
    else if (mode & SPI_TX_DUAL)
        tr.tx_nbits = 2;
    if (mode & SPI_RX_QUAD)
        tr.rx_nbits = 4;
    else if (mode & SPI_RX_DUAL)
        tr.rx_nbits = 2;
    if (!(mode & SPI_LOOP)) {
        if (mode & (SPI_TX_QUAD | SPI_TX_DUAL))
            tr.rx_buf = 0;
        else if (mode & (SPI_RX_QUAD | SPI_RX_DUAL))
            tr.tx_buf = 0;
    }

    ret = ioctl(fd, SPI_IOC_MESSAGE(1), &tr);
    if (ret < 1)
        pabort("can't send spi message");

    if (1) {
        printf("TX: ");
        puts("");
        for (ret = 0; ret < len; ret++) {
            printf("%.2X ", tx[ret]);
        }
        puts("");
        
        printf("RX: ");
        puts("");
        for (ret = 0; ret < len; ret++) {
            printf("%.2X ", rx[ret]);
        }
        puts("");
    }
}

static void print_spi_config(int fd)
{
    int ret = 0;
    
    printf("SPI Configuration:\n");
    printf("Device: %s\n", device);
    printf("Mode: 0x%X\n", mode);
    printf("Bits per word: %d\n", bits);
    printf("Max speed: %d Hz (%d KHz)\n", speed, speed/1000);
    printf("Delay: %d usec\n", delay);
    printf("\n");
}

int main(int argc, char *argv[])
{
    int ret = 0;
    int fd;
    uint8_t default_tx[] = {
        0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
        0x40, 0x00, 0x00, 0x00, 0x00, 0x95,
        0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
        0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
        0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
        0xF0, 0x0D,
    };
    uint8_t default_rx[ARRAY_SIZE(default_tx)] = {0, };
    char *input_tx;

    parse_opts(argc, argv);

    fd = open(device, O_RDWR);
    if (fd < 0)
        pabort("can't open device");

    /*
     * spi mode
     */
    ret = ioctl(fd, SPI_IOC_WR_MODE32, &mode);
    if (ret == -1)
        pabort("can't set spi mode");

    ret = ioctl(fd, SPI_IOC_RD_MODE32, &mode);
    if (ret == -1)
        pabort("can't get spi mode");

    /*
     * bits per word
     */
    ret = ioctl(fd, SPI_IOC_WR_BITS_PER_WORD, &bits);
    if (ret == -1)
        pabort("can't set bits per word");

    ret = ioctl(fd, SPI_IOC_RD_BITS_PER_WORD, &bits);
    if (ret == -1)
        pabort("can't get bits per word");

    /*
     * max speed hz
     */
    ret = ioctl(fd, SPI_IOC_WR_MAX_SPEED_HZ, &speed);
    if (ret == -1)
        pabort("can't set max speed hz");

    ret = ioctl(fd, SPI_IOC_RD_MAX_SPEED_HZ, &speed);
    if (ret == -1)
        pabort("can't get max speed hz");

    print_spi_config(fd);

    printf("Sending default test pattern...\n");
    while (1) {
        default_tx[0] = default_tx[0] + 1;
        transfer(fd, default_tx, default_rx, sizeof(default_tx));   
        usleep(500000); // 100ms延时     
    }


    close(fd);
    printf("测试完成!\n");
    return ret;
}
