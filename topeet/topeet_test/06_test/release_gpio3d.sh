#!/bin/bash
echo "释放GPIO3_D1-D3..."

# 释放GPIO
for pin in 125 126 127; do
    echo $pin > /sys/class/gpio/unexport 2>/dev/null || true
done

# 重新绑定驱动
echo "重新绑定pinctrl驱动..."
echo "pinctrl-rockchip-pinctrl" > /sys/bus/platform/drivers/pinctrl-rockchip/unbind 2>/dev/null || true
sleep 1
echo "pinctrl-rockchip-pinctrl" > /sys/bus/platform/drivers/pinctrl-rockchip/bind 2>/dev/null || true

echo "重新绑定SPI驱动..."
echo "feb00000.spi" > /sys/bus/platform/drivers/rockchip-spi/unbind 2>/dev/null || true
sleep 1
echo "feb00000.spi" > /sys/bus/platform/drivers/rockchip-spi/bind 2>/dev/null || true

echo "完成!"
