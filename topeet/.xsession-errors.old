dbus-update-activation-environment: setting DBUS_SESSION_BUS_ADDRESS=unix:path=/run/user/1000/bus
dbus-update-activation-environment: setting DISPLAY=:0
dbus-update-activation-environment: setting XAUTHORITY=/home/<USER>/.Xauthority
/etc/X11/Xsession.d/30x11-common_xresources: 行 16: has_option: 未找到命令
/etc/X11/Xsession.d/75dbus_dbus-launch: 行 9: has_option: 未找到命令
dbus-update-activation-environment: setting QT_ACCESSIBILITY=1
/etc/X11/Xsession.d/90x11-common_ssh-agent: 行 9: has_option: 未找到命令
dbus-update-activation-environment: setting SHELL=/bin/bash
dbus-update-activation-environment: setting QT_ACCESSIBILITY=1
dbus-update-activation-environment: setting XDG_CONFIG_DIRS=/etc/xdg/xdg-xubuntu:/etc/xdg
dbus-update-activation-environment: setting XDG_SESSION_PATH=/org/freedesktop/DisplayManager/Session0
dbus-update-activation-environment: setting UMS_RO=0
dbus-update-activation-environment: setting GTK_IM_MODULE=fcitx
dbus-update-activation-environment: setting CLUTTER_BACKEND=x11
dbus-update-activation-environment: setting GST_GL_PLATFORM=egl
dbus-update-activation-environment: setting COGL_DRIVER=gles2
dbus-update-activation-environment: setting LANGUAGE=zh_CN:zh:en_US:en
dbus-update-activation-environment: setting UMS_MOUNTPOINT=/mnt/ums
dbus-update-activation-environment: setting UMS_MOUNT=0
dbus-update-activation-environment: setting XMODIFIERS=@im=fcitx
dbus-update-activation-environment: setting DESKTOP_SESSION=xubuntu
dbus-update-activation-environment: setting PWD=/home/<USER>
dbus-update-activation-environment: setting XDG_SESSION_DESKTOP=xubuntu
dbus-update-activation-environment: setting LOGNAME=topeet
dbus-update-activation-environment: setting QT_QPA_PLATFORMTHEME=gtk2
dbus-update-activation-environment: setting XDG_SESSION_TYPE=x11
dbus-update-activation-environment: setting GPG_AGENT_INFO=/run/user/1000/gnupg/S.gpg-agent:0:1
dbus-update-activation-environment: setting XAUTHORITY=/home/<USER>/.Xauthority
dbus-update-activation-environment: setting UMS_SIZE=256M
dbus-update-activation-environment: setting XDG_GREETER_DATA_DIR=/var/lib/lightdm-data/topeet
dbus-update-activation-environment: setting GDM_LANG=zh_CN
dbus-update-activation-environment: setting HOME=/home/<USER>
dbus-update-activation-environment: setting IM_CONFIG_PHASE=1
dbus-update-activation-environment: setting LANG=zh_CN.UTF-8
dbus-update-activation-environment: setting XDG_CURRENT_DESKTOP=XFCE
dbus-update-activation-environment: setting XDG_SEAT_PATH=/org/freedesktop/DisplayManager/Seat0
dbus-update-activation-environment: setting CLUTTER_IM_MODULE=fcitx
dbus-update-activation-environment: setting XDG_SESSION_CLASS=user
dbus-update-activation-environment: setting TERM=dumb
dbus-update-activation-environment: setting GTK_OVERLAY_SCROLLING=0
dbus-update-activation-environment: setting USER=topeet
dbus-update-activation-environment: setting XSERVER_FREEZE_DISPLAY=/tmp/.freeze_xserver
dbus-update-activation-environment: setting UMS_FILE=/userdata/ums_shared.img
dbus-update-activation-environment: setting DISPLAY=:0
dbus-update-activation-environment: setting UMS_FSTYPE=vfat
dbus-update-activation-environment: setting SHLVL=1
dbus-update-activation-environment: setting USB_FUNCS=adb
dbus-update-activation-environment: setting QT_IM_MODULE=fcitx
dbus-update-activation-environment: setting GST_GL_API=gles2
dbus-update-activation-environment: setting XDG_RUNTIME_DIR=/run/user/1000
dbus-update-activation-environment: setting LC_ALL=zh_CN.UTF-8
dbus-update-activation-environment: setting XDG_DATA_DIRS=/usr/share/xfce4:/usr/share/xubuntu:/usr/share/gnome:/usr/local/share:/usr/share:/var/lib/snapd/desktop
dbus-update-activation-environment: setting PATH=/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin
dbus-update-activation-environment: setting GDMSESSION=xubuntu
dbus-update-activation-environment: setting QTWEBENGINE_CHROMIUM_FLAGS=--no-sandbox --disable-es3-gl-context --ignore-gpu-blacklist --ignore-gpu-blocklist --enable-accelerated-video-decode
dbus-update-activation-environment: setting DBUS_SESSION_BUS_ADDRESS=unix:path=/run/user/1000/bus
dbus-update-activation-environment: setting _=/usr/bin/dbus-update-activation-environment
libGL error: glx: failed to create dri3 screen
libGL error: failed to load driver: rockchip
libGL error: glx: failed to create dri2 screen
libGL error: failed to load driver: rockchip
/usr/bin/iceauth:  creating new authority file /run/user/1000/ICEauthority
** Message: 17:30:07.183: couldn't access control socket: /run/user/1000/keyring/control: 没有那个文件或目录

(xfwm4:1535): GLib-CRITICAL **: 17:30:08.675: g_str_has_prefix: assertion 'prefix != NULL' failed
libGL error: glx: failed to create dri3 screen
libGL error: failed to load driver: rockchip
libGL error: glx: failed to create dri2 screen
libGL error: failed to load driver: rockchip

(xfwm4:1535): xfwm4-WARNING **: 17:30:11.144: Unsupported GL renderer (llvmpipe (LLVM 15.0.7, 128 bits)).

(xfsettingsd:1576): GLib-CRITICAL **: 17:30:11.511: g_str_has_prefix: assertion 'prefix != NULL' failed

(xfsettingsd:1576): GLib-GObject-CRITICAL **: 17:30:11.516: g_value_get_string: assertion 'G_VALUE_HOLDS_STRING (value)' failed

(xfsettingsd:1576): GLib-GObject-CRITICAL **: 17:30:11.556: g_value_get_string: assertion 'G_VALUE_HOLDS_STRING (value)' failed

(xfsettingsd:1576): GLib-GObject-CRITICAL **: 17:30:11.567: g_value_get_string: assertion 'G_VALUE_HOLDS_STRING (value)' failed

(wrapper-2.0:1602): GLib-GIO-CRITICAL **: 17:30:12.534: g_file_new_for_path: assertion 'path != NULL' failed

(wrapper-2.0:1602): GLib-GIO-CRITICAL **: 17:30:12.535: g_file_monitor_file: assertion 'G_IS_FILE (file)' failed

(wrapper-2.0:1602): GLib-GObject-WARNING **: 17:30:12.535: invalid (NULL) pointer instance

(wrapper-2.0:1602): GLib-GObject-CRITICAL **: 17:30:12.535: g_signal_connect_data: assertion 'G_TYPE_CHECK_INSTANCE (instance)' failed

(wrapper-2.0:1602): Gtk-WARNING **: 17:30:12.535: Attempting to add a widget with type GtkToggleButton to a container of type XfcePanelPlugin, but the widget is already inside a container of type XfcePanelPlugin, please remove the widget from its existing container first.

(wrapper-2.0:1600): Gtk-WARNING **: 17:30:12.570: Negative content width -3 (allocation 1, extents 2x2) while allocating gadget (node button, owner GtkToggleButton)

(wrapper-2.0:1602): Gtk-WARNING **: 17:30:12.591: Negative content width -3 (allocation 1, extents 2x2) while allocating gadget (node button, owner GtkToggleButton)

(polkit-gnome-authentication-agent-1:1629): GLib-CRITICAL **: 17:30:12.596: g_variant_new_string: assertion 'string != NULL' failed

(polkit-gnome-authentication-agent-1:1629): polkit-gnome-1-WARNING **: 17:30:12.598: Failed to register client: GDBus.Error:org.freedesktop.DBus.Error.ServiceUnknown: The name org.gnome.SessionManager was not provided by any .service files

** (xfce4-power-manager:1646): WARNING **: 17:30:12.612: Failed to get name owner: GDBus.Error:org.freedesktop.DBus.Error.NameHasNoOwner: Could not get owner of name 'org.freedesktop.PowerManagement': no such name


** (xfce4-power-manager:1646): WARNING **: 17:30:12.612: Failed to get name owner: GDBus.Error:org.freedesktop.DBus.Error.NameHasNoOwner: Could not get owner of name 'org.xfce.PowerManager': no such name


** (wrapper-2.0:1605): WARNING **: 17:30:12.653: No outputs have backlight property

(wrapper-2.0:1607): Gtk-WARNING **: 17:30:12.713: Negative content width -3 (allocation 1, extents 2x2) while allocating gadget (node button, owner PulseaudioButton)

(process:1688): ayatana-indicator-application-service-WARNING **: 17:30:12.729: Unable to get watcher name 'org.kde.StatusNotifierWatcher'

(xfce4-power-manager:1646): GLib-GObject-WARNING **: 17:30:12.765: ../../../gobject/gsignal.c:2613: signal 'Changed' is invalid for instance '0x5567b8a910' of type 'GDBusProxy'

(wrapper-2.0:1605): Gtk-CRITICAL **: 17:30:12.766: gtk_icon_theme_has_icon: assertion 'icon_name != NULL' failed

(wrapper-2.0:1605): Gtk-CRITICAL **: 17:30:12.766: gtk_icon_theme_has_icon: assertion 'icon_name != NULL' failed

(wrapper-2.0:1605): xfce4-power-manager-plugin-WARNING **: 17:30:12.775: Xfce4-power-manager: The panel plugin is present, so the tray icon gets disabled.

(wrapper-2.0:1605): Gtk-CRITICAL **: 17:30:12.783: gtk_icon_theme_has_icon: assertion 'icon_name != NULL' failed

(wrapper-2.0:1605): Gtk-CRITICAL **: 17:30:12.788: gtk_icon_theme_has_icon: assertion 'icon_name != NULL' failed

(wrapper-2.0:1605): Gtk-WARNING **: 17:30:12.810: Negative content width -3 (allocation 1, extents 2x2) while allocating gadget (node button, owner PowerManagerButton)

(xfce4-power-manager:1646): xfce4-power-manager-WARNING **: 17:30:12.812: could not map keysym 1008ffa8 to keycode


** (xfce4-power-manager:1646): WARNING **: 17:30:12.828: No outputs have backlight property
/usr/libexec/evolution-data-server/evolution-alarm-notify: symbol lookup error: /lib/aarch64-linux-gnu/libwebkit2gtk-4.0.so.37: undefined symbol: gbm_bo_create_with_modifiers2

** (xfce4-screensaver:1674): WARNING **: 17:30:12.890: screensaver already running in this session

(xfce4-power-manager:1646): xfce4-power-manager-WARNING **: 17:30:12.950: Unable to set the kernel brightness switch parameter to 0.

(xfce4-power-manager:1646): xfce4-power-manager-WARNING **: 17:30:12.957: Failed to get keyboard max brightness level : GDBus.Error:org.freedesktop.DBus.Error.UnknownMethod: 对象在路径“/org/freedesktop/UPower/KbdBacklight”处不存在
blueman-applet 17.30.12 WARNING  PluginManager:147 __load_plugin: Not loading PPPSupport because its conflict has higher priority
blueman-applet 17.30.12 WARNING  PluginManager:147 __load_plugin: Not loading DhcpClient because its conflict has higher priority
blueman-applet 17.30.13 WARNING  DiscvManager:119 update_menuitems: warning: Adapter is None

** (xiccd:1671): WARNING **: 17:32:54.558: EDID is empty

** (xiccd:1671): WARNING **: 17:32:54.562: EDID is empty

(thunar-volman:1832): GVFS-RemoteVolumeMonitor-WARNING **: 17:33:14.116: remote volume monitor with dbus name org.gtk.vfs.GoaVolumeMonitor is not supported
thunar-volman: Unsupported USB device type "usb".

(thunar-volman:1858): GVFS-RemoteVolumeMonitor-WARNING **: 17:33:14.221: remote volume monitor with dbus name org.gtk.vfs.GoaVolumeMonitor is not supported

(thunar-volman:1867): GVFS-RemoteVolumeMonitor-WARNING **: 17:33:14.317: remote volume monitor with dbus name org.gtk.vfs.GoaVolumeMonitor is not supported
thunar-volman: Unsupported input device type "/dev/input/event7".

(thunar-volman:1877): GVFS-RemoteVolumeMonitor-WARNING **: 17:33:14.464: remote volume monitor with dbus name org.gtk.vfs.GoaVolumeMonitor is not supported
thunar-volman: Unsupported input device type "/dev/input/event6".

(thunar-volman:1881): GVFS-RemoteVolumeMonitor-WARNING **: 17:33:14.569: remote volume monitor with dbus name org.gtk.vfs.GoaVolumeMonitor is not supported
thunar-volman: Unsupported input device type "(null)".

(thunar-volman:1885): GVFS-RemoteVolumeMonitor-WARNING **: 17:33:14.700: remote volume monitor with dbus name org.gtk.vfs.GoaVolumeMonitor is not supported

(thunar-volman:1889): GVFS-RemoteVolumeMonitor-WARNING **: 17:33:14.806: remote volume monitor with dbus name org.gtk.vfs.GoaVolumeMonitor is not supported
thunar-volman: Unsupported input device type "(null)".

(thunar-volman:1893): GVFS-RemoteVolumeMonitor-WARNING **: 17:33:14.926: remote volume monitor with dbus name org.gtk.vfs.GoaVolumeMonitor is not supported
thunar-volman: Unsupported USB device type "usbhid".

(thunar-volman:1897): GVFS-RemoteVolumeMonitor-WARNING **: 17:33:15.038: remote volume monitor with dbus name org.gtk.vfs.GoaVolumeMonitor is not supported
thunar-volman: Unsupported USB device type "usbhid".
