dbus-update-activation-environment: setting DBUS_SESSION_BUS_ADDRESS=unix:path=/run/user/1000/bus
dbus-update-activation-environment: setting DISPLAY=:0
dbus-update-activation-environment: setting XAUTHORITY=/home/<USER>/.Xauthority
/etc/X11/Xsession.d/30x11-common_xresources: 行 16: has_option: 未找到命令
/etc/X11/Xsession.d/75dbus_dbus-launch: 行 9: has_option: 未找到命令
dbus-update-activation-environment: setting QT_ACCESSIBILITY=1
/etc/X11/Xsession.d/90x11-common_ssh-agent: 行 9: has_option: 未找到命令
dbus-update-activation-environment: setting SHELL=/bin/bash
dbus-update-activation-environment: setting QT_ACCESSIBILITY=1
dbus-update-activation-environment: setting XDG_CONFIG_DIRS=/etc/xdg/xdg-xubuntu:/etc/xdg
dbus-update-activation-environment: setting XDG_SESSION_PATH=/org/freedesktop/DisplayManager/Session0
dbus-update-activation-environment: setting UMS_RO=0
dbus-update-activation-environment: setting GTK_IM_MODULE=fcitx
dbus-update-activation-environment: setting CLUTTER_BACKEND=x11
dbus-update-activation-environment: setting GST_GL_PLATFORM=egl
dbus-update-activation-environment: setting COGL_DRIVER=gles2
dbus-update-activation-environment: setting LANGUAGE=zh_CN:zh:en_US:en
dbus-update-activation-environment: setting UMS_MOUNTPOINT=/mnt/ums
dbus-update-activation-environment: setting UMS_MOUNT=0
dbus-update-activation-environment: setting XMODIFIERS=@im=fcitx
dbus-update-activation-environment: setting DESKTOP_SESSION=xubuntu
dbus-update-activation-environment: setting PWD=/home/<USER>
dbus-update-activation-environment: setting XDG_SESSION_DESKTOP=xubuntu
dbus-update-activation-environment: setting LOGNAME=topeet
dbus-update-activation-environment: setting QT_QPA_PLATFORMTHEME=gtk2
dbus-update-activation-environment: setting XDG_SESSION_TYPE=x11
dbus-update-activation-environment: setting GPG_AGENT_INFO=/run/user/1000/gnupg/S.gpg-agent:0:1
dbus-update-activation-environment: setting XAUTHORITY=/home/<USER>/.Xauthority
dbus-update-activation-environment: setting UMS_SIZE=256M
dbus-update-activation-environment: setting XDG_GREETER_DATA_DIR=/var/lib/lightdm-data/topeet
dbus-update-activation-environment: setting GDM_LANG=zh_CN
dbus-update-activation-environment: setting HOME=/home/<USER>
dbus-update-activation-environment: setting IM_CONFIG_PHASE=1
dbus-update-activation-environment: setting LANG=zh_CN.UTF-8
dbus-update-activation-environment: setting XDG_CURRENT_DESKTOP=XFCE
dbus-update-activation-environment: setting XDG_SEAT_PATH=/org/freedesktop/DisplayManager/Seat0
dbus-update-activation-environment: setting CLUTTER_IM_MODULE=fcitx
dbus-update-activation-environment: setting XDG_SESSION_CLASS=user
dbus-update-activation-environment: setting TERM=dumb
dbus-update-activation-environment: setting GTK_OVERLAY_SCROLLING=0
dbus-update-activation-environment: setting USER=topeet
dbus-update-activation-environment: setting XSERVER_FREEZE_DISPLAY=/tmp/.freeze_xserver
dbus-update-activation-environment: setting UMS_FILE=/userdata/ums_shared.img
dbus-update-activation-environment: setting DISPLAY=:0
dbus-update-activation-environment: setting UMS_FSTYPE=vfat
dbus-update-activation-environment: setting SHLVL=1
dbus-update-activation-environment: setting USB_FUNCS=adb
dbus-update-activation-environment: setting QT_IM_MODULE=fcitx
dbus-update-activation-environment: setting GST_GL_API=gles2
dbus-update-activation-environment: setting XDG_RUNTIME_DIR=/run/user/1000
dbus-update-activation-environment: setting LC_ALL=zh_CN.UTF-8
dbus-update-activation-environment: setting XDG_DATA_DIRS=/usr/share/xfce4:/usr/share/xubuntu:/usr/share/gnome:/usr/local/share:/usr/share:/var/lib/snapd/desktop
dbus-update-activation-environment: setting PATH=/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin
dbus-update-activation-environment: setting GDMSESSION=xubuntu
dbus-update-activation-environment: setting QTWEBENGINE_CHROMIUM_FLAGS=--no-sandbox --disable-es3-gl-context --ignore-gpu-blacklist --ignore-gpu-blocklist --enable-accelerated-video-decode
dbus-update-activation-environment: setting DBUS_SESSION_BUS_ADDRESS=unix:path=/run/user/1000/bus
dbus-update-activation-environment: setting _=/usr/bin/dbus-update-activation-environment
libGL error: glx: failed to create dri3 screen
libGL error: failed to load driver: rockchip
libGL error: glx: failed to create dri2 screen
libGL error: failed to load driver: rockchip
/usr/bin/iceauth:  creating new authority file /run/user/1000/ICEauthority
** Message: 09:12:38.213: couldn't access control socket: /run/user/1000/keyring/control: 没有那个文件或目录
libGL error: glx: failed to create dri3 screen
libGL error: failed to load driver: rockchip
libGL error: glx: failed to create dri2 screen
libGL error: failed to load driver: rockchip

(xfwm4:1617): xfwm4-WARNING **: 09:12:39.871: Unsupported GL renderer (llvmpipe (LLVM 15.0.7, 128 bits)).

** (wrapper-2.0:1681): WARNING **: 09:12:40.867: No outputs have backlight property

(wrapper-2.0:1681): Gtk-CRITICAL **: 09:12:40.916: gtk_icon_theme_has_icon: assertion 'icon_name != NULL' failed

(wrapper-2.0:1681): Gtk-CRITICAL **: 09:12:40.916: gtk_icon_theme_has_icon: assertion 'icon_name != NULL' failed

(wrapper-2.0:1681): Gtk-CRITICAL **: 09:12:40.929: gtk_icon_theme_has_icon: assertion 'icon_name != NULL' failed

(wrapper-2.0:1681): Gtk-CRITICAL **: 09:12:40.932: gtk_icon_theme_has_icon: assertion 'icon_name != NULL' failed

(wrapper-2.0:1679): GLib-GIO-CRITICAL **: 09:12:40.959: g_file_new_for_path: assertion 'path != NULL' failed

(wrapper-2.0:1679): GLib-GIO-CRITICAL **: 09:12:40.959: g_file_monitor_file: assertion 'G_IS_FILE (file)' failed

(wrapper-2.0:1679): GLib-GObject-WARNING **: 09:12:40.959: invalid (NULL) pointer instance

(wrapper-2.0:1679): GLib-GObject-CRITICAL **: 09:12:40.959: g_signal_connect_data: assertion 'G_TYPE_CHECK_INSTANCE (instance)' failed

(wrapper-2.0:1679): Gtk-WARNING **: 09:12:40.959: Attempting to add a widget with type GtkToggleButton to a container of type XfcePanelPlugin, but the widget is already inside a container of type XfcePanelPlugin, please remove the widget from its existing container first.

(wrapper-2.0:1681): Gtk-WARNING **: 09:12:40.988: Negative content width -3 (allocation 1, extents 2x2) while allocating gadget (node button, owner PowerManagerButton)

(wrapper-2.0:1676): Gtk-WARNING **: 09:12:40.998: Negative content width -3 (allocation 1, extents 2x2) while allocating gadget (node button, owner GtkToggleButton)

(wrapper-2.0:1685): Gtk-WARNING **: 09:12:41.017: Negative content width -3 (allocation 1, extents 2x2) while allocating gadget (node button, owner PulseaudioButton)

(wrapper-2.0:1679): Gtk-WARNING **: 09:12:41.035: Negative content width -3 (allocation 1, extents 2x2) while allocating gadget (node button, owner GtkToggleButton)

** (xfce4-power-manager:1741): WARNING **: 09:12:41.069: Failed to get name owner: GDBus.Error:org.freedesktop.DBus.Error.NameHasNoOwner: Could not get owner of name 'org.freedesktop.PowerManagement': no such name


** (xfce4-power-manager:1741): WARNING **: 09:12:41.070: Failed to get name owner: GDBus.Error:org.freedesktop.DBus.Error.NameHasNoOwner: Could not get owner of name 'org.xfce.PowerManager': no such name


(polkit-gnome-authentication-agent-1:1724): GLib-CRITICAL **: 09:12:41.096: g_variant_new_string: assertion 'string != NULL' failed

(polkit-gnome-authentication-agent-1:1724): polkit-gnome-1-WARNING **: 09:12:41.097: Failed to register client: GDBus.Error:org.freedesktop.DBus.Error.ServiceUnknown: The name org.gnome.SessionManager was not provided by any .service files

(process:1771): ayatana-indicator-application-service-WARNING **: 09:12:41.131: Unable to get watcher name 'org.kde.StatusNotifierWatcher'

(xfce4-power-manager:1741): GLib-GObject-WARNING **: 09:12:41.180: ../../../gobject/gsignal.c:2613: signal 'Changed' is invalid for instance '0x559611c120' of type 'GDBusProxy'

(xfce4-power-manager:1741): xfce4-power-manager-WARNING **: 09:12:41.235: could not map keysym 1008ffa8 to keycode


** (xfce4-power-manager:1741): WARNING **: 09:12:41.247: No outputs have backlight property

(xfce4-power-manager:1741): xfce4-power-manager-WARNING **: 09:12:41.257: It seems the kernel brightness switch handling value was not restored properly on exit last time, xfce4-power-manager will try to restore it this time.
/usr/libexec/evolution-data-server/evolution-alarm-notify: symbol lookup error: /lib/aarch64-linux-gnu/libwebkit2gtk-4.0.so.37: undefined symbol: gbm_bo_create_with_modifiers2

** (xfce4-screensaver:1761): WARNING **: 09:12:41.289: screensaver already running in this session

(xfce4-power-manager:1741): xfce4-power-manager-WARNING **: 09:12:41.350: Unable to set the kernel brightness switch parameter to 0.

(xfce4-power-manager:1741): xfce4-power-manager-WARNING **: 09:12:41.358: Failed to get keyboard max brightness level : GDBus.Error:org.freedesktop.DBus.Error.UnknownMethod: 对象在路径“/org/freedesktop/UPower/KbdBacklight”处不存在
blueman-applet 09.12.41 WARNING  PluginManager:147 __load_plugin: Not loading PPPSupport because its conflict has higher priority
blueman-applet 09.12.41 WARNING  PluginManager:147 __load_plugin: Not loading DhcpClient because its conflict has higher priority
blueman-applet 09.12.41 WARNING  DiscvManager:119 update_menuitems: warning: Adapter is None

** (xiccd:1760): WARNING **: 14:53:01.574: EDID is empty

** (xiccd:1760): WARNING **: 14:53:01.583: EDID is empty

** (xiccd:1760): WARNING **: 14:53:01.587: EDID is empty

(thunar-volman:5292): GVFS-RemoteVolumeMonitor-WARNING **: 15:00:19.732: remote volume monitor with dbus name org.gtk.vfs.GoaVolumeMonitor is not supported
thunar-volman: Unsupported USB device type "usb".

(thunar-volman:5323): GVFS-RemoteVolumeMonitor-WARNING **: 15:00:19.828: remote volume monitor with dbus name org.gtk.vfs.GoaVolumeMonitor is not supported

(thunar-volman:5327): GVFS-RemoteVolumeMonitor-WARNING **: 15:00:19.894: remote volume monitor with dbus name org.gtk.vfs.GoaVolumeMonitor is not supported
thunar-volman: Unsupported input device type "/dev/input/event11".

(thunar-volman:5331): GVFS-RemoteVolumeMonitor-WARNING **: 15:00:19.975: remote volume monitor with dbus name org.gtk.vfs.GoaVolumeMonitor is not supported
thunar-volman: Unsupported input device type "/dev/input/event9".

(thunar-volman:5335): GVFS-RemoteVolumeMonitor-WARNING **: 15:00:20.048: remote volume monitor with dbus name org.gtk.vfs.GoaVolumeMonitor is not supported
thunar-volman: Unsupported input device type "/dev/input/event10".

(thunar-volman:5339): GVFS-RemoteVolumeMonitor-WARNING **: 15:00:20.130: remote volume monitor with dbus name org.gtk.vfs.GoaVolumeMonitor is not supported
thunar-volman: Unsupported input device type "(null)".

(thunar-volman:5345): GVFS-RemoteVolumeMonitor-WARNING **: 15:00:20.213: remote volume monitor with dbus name org.gtk.vfs.GoaVolumeMonitor is not supported

(thunar-volman:5349): GVFS-RemoteVolumeMonitor-WARNING **: 15:00:20.301: remote volume monitor with dbus name org.gtk.vfs.GoaVolumeMonitor is not supported
thunar-volman: Unsupported input device type "(null)".

(thunar-volman:5353): GVFS-RemoteVolumeMonitor-WARNING **: 15:00:20.390: remote volume monitor with dbus name org.gtk.vfs.GoaVolumeMonitor is not supported

(thunar-volman:5357): GVFS-RemoteVolumeMonitor-WARNING **: 15:00:20.478: remote volume monitor with dbus name org.gtk.vfs.GoaVolumeMonitor is not supported
thunar-volman: Unsupported USB device type "usbhid".

(thunar-volman:5361): GVFS-RemoteVolumeMonitor-WARNING **: 15:00:20.567: remote volume monitor with dbus name org.gtk.vfs.GoaVolumeMonitor is not supported
thunar-volman: Unsupported USB device type "usbhid".

(Thunar:1670): GVFS-RemoteVolumeMonitor-WARNING **: 15:00:54.726: remote volume monitor with dbus name org.gtk.vfs.GoaVolumeMonitor is not supported

(thunar-volman:8508): GVFS-RemoteVolumeMonitor-WARNING **: 15:53:23.611: remote volume monitor with dbus name org.gtk.vfs.GoaVolumeMonitor is not supported
thunar-volman: Unsupported USB device type "usb".

(thunar-volman:8523): GVFS-RemoteVolumeMonitor-WARNING **: 15:53:23.691: remote volume monitor with dbus name org.gtk.vfs.GoaVolumeMonitor is not supported
thunar-volman: Unsupported USB device type "btusb".

(thunar-volman:8527): GVFS-RemoteVolumeMonitor-WARNING **: 15:53:23.776: remote volume monitor with dbus name org.gtk.vfs.GoaVolumeMonitor is not supported
thunar-volman: Unsupported USB device type "rtl8723du".

(thunar-volman:8532): GVFS-RemoteVolumeMonitor-WARNING **: 15:53:23.863: remote volume monitor with dbus name org.gtk.vfs.GoaVolumeMonitor is not supported
thunar-volman: Unsupported USB device type "btusb".
