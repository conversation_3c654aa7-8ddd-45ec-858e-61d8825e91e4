; Thunar GtkAccelMap rc-file         -*- scheme -*-
; this file is an automated accelerator map dump
;
; (gtk_accel_path "<Actions>/ThunarLauncher/delete-3" "<Shift>KP_Delete")
; (gtk_accel_path "<Actions>/ThunarLauncher/delete" "")
; (gtk_accel_path "<Actions>/ThunarStandardView/invert-selection" "")
; (gtk_accel_path "<Actions>/ThunarLauncher/delete-2" "<Shift>Delete")
; (gtk_accel_path "<Actions>/ThunarLauncher/open-in-new-tab" "<Primary><Shift>p")
; (gtk_accel_path "<Actions>/ThunarWindow/empty-trash" "")
; (gtk_accel_path "<Actions>/ThunarWindow/zoom-in" "<Primary>KP_Add")
(gtk_accel_path "<Actions>/ThunarActions/uca-action-3-3" "<Primary><Shift>f")
; (gtk_accel_path "<Actions>/ThunarLauncher/open" "<Primary>o")
; (gtk_accel_path "<Actions>/ThunarWindow/reload" "<Primary>r")
; (gtk_accel_path "<Actions>/ThunarWindow/view-side-pane-menu" "")
; (gtk_accel_path "<Actions>/ThunarStandardView/sort-ascending" "")
; (gtk_accel_path "<Actions>/ThunarWindow/open-network" "")
; (gtk_accel_path "<Actions>/ThunarWindow/contents/help-menu" "")
; (gtk_accel_path "<Actions>/ThunarLauncher/move-to-trash" "")
; (gtk_accel_path "<Actions>/ThunarStandardView/back-alt" "BackSpace")
; (gtk_accel_path "<Actions>/ThunarLauncher/paste" "<Primary>v")
; (gtk_accel_path "<Actions>/ThunarWindow/switch-next-tab" "<Primary>Page_Down")
; (gtk_accel_path "<Actions>/ThunarWindow/open-file-menu" "F10")
; (gtk_accel_path "<Actions>/ThunarWindow/view-as-compact-list" "<Primary>3")
; (gtk_accel_path "<Actions>/ThunarWindow/about" "")
; (gtk_accel_path "<Actions>/ThunarWindow/open-computer" "")
; (gtk_accel_path "<Actions>/ThunarWindow/clear-directory-specific-settings" "")
; (gtk_accel_path "<Actions>/ThunarWindow/open-file-system" "")
; (gtk_accel_path "<Actions>/ThunarWindow/open-parent" "<Alt>Up")
; (gtk_accel_path "<Actions>/ThunarWindow/view-menu" "")
; (gtk_accel_path "<Actions>/ThunarStandardView/properties" "<Alt>Return")
; (gtk_accel_path "<Actions>/ThunarLauncher/open-in-new-window" "<Primary><Shift>o")
; (gtk_accel_path "<Actions>/ThunarStandardView/back" "<Alt>Left")
; (gtk_accel_path "<Actions>/ThunarLauncher/trash-delete" "Delete")
; (gtk_accel_path "<Actions>/ThunarWindow/sendto-menu" "")
; (gtk_accel_path "<Actions>/ThunarBookmarks/0c0c97d167afda23efd60ae84ebf2813" "")
; (gtk_accel_path "<Actions>/ThunarWindow/reload-alt" "F5")
; (gtk_accel_path "<Actions>/ThunarWindow/zoom-out-alt" "<Primary>minus")
; (gtk_accel_path "<Actions>/ThunarStandardView/select-all-files" "<Primary>a")
; (gtk_accel_path "<Actions>/ThunarStandardView/select-by-pattern" "<Primary>s")
; (gtk_accel_path "<Actions>/ThunarStandardView/rename" "F2")
; (gtk_accel_path "<Actions>/ThunarStandardView/create-document" "")
; (gtk_accel_path "<Actions>/ThunarShortcutsPane/sendto-shortcuts" "")
; (gtk_accel_path "<Actions>/ThunarWindow/close-tab" "<Primary>w")
; (gtk_accel_path "<Actions>/ThunarWindow/view-side-pane-tree" "<Primary>e")
; (gtk_accel_path "<Actions>/ThunarBookmarks/e203523270cce431c48f315b5864c2c4" "")
(gtk_accel_path "<Actions>/ThunarActions/uca-action-2-2" "F4")
; (gtk_accel_path "<Actions>/ThunarWindow/toggle-side-pane" "F9")
; (gtk_accel_path "<Actions>/ThunarWindow/open-home" "<Alt>Home")
; (gtk_accel_path "<Actions>/ThunarWindow/open-location-alt" "<Alt>d")
; (gtk_accel_path "<Actions>/ThunarWindow/open-templates" "")
; (gtk_accel_path "<Actions>/ThunarLauncher/execute" "")
; (gtk_accel_path "<Actions>/ThunarWindow/close-window" "<Primary>q")
; (gtk_accel_path "<Actions>/ThunarLauncher/cut" "<Primary>x")
; (gtk_accel_path "<Actions>/ThunarLauncher/sendto-desktop" "")
; (gtk_accel_path "<Actions>/ThunarWindow/zoom-in-alt2" "<Primary>equal")
; (gtk_accel_path "<Actions>/ThunarWindow/view-location-selector-toolbar" "")
; (gtk_accel_path "<Actions>/ThunarBookmarks/0a241358a9888076d16dbc2171664717" "")
; (gtk_accel_path "<Actions>/ThunarLauncher/open-with-other" "")
; (gtk_accel_path "<Actions>/ThunarWindow/zoom-out" "<Primary>KP_Subtract")
; (gtk_accel_path "<Actions>/ThunarStandardView/duplicate" "")
; (gtk_accel_path "<Actions>/ThunarWindow/view-side-pane-shortcuts" "<Primary>b")
; (gtk_accel_path "<Actions>/ThunarWindow/edit-menu" "")
; (gtk_accel_path "<Actions>/ThunarWindow/contents" "F1")
; (gtk_accel_path "<Actions>/ThunarWindow/preferences" "")
; (gtk_accel_path "<Actions>/ThunarWindow/zoom-in-alt1" "<Primary>plus")
; (gtk_accel_path "<Actions>/ThunarWindow/switch-previous-tab" "<Primary>Page_Up")
; (gtk_accel_path "<Actions>/ThunarStandardView/arrange-items-menu" "")
; (gtk_accel_path "<Actions>/ThunarStandardView/sort-by-type" "")
; (gtk_accel_path "<Actions>/ThunarWindow/view-location-selector-menu" "")
; (gtk_accel_path "<Actions>/ThunarWindow/view-statusbar" "")
; (gtk_accel_path "<Actions>/ThunarWindow/close-all-windows" "<Primary><Shift>w")
; (gtk_accel_path "<Actions>/ThunarWindow/open-trash" "")
; (gtk_accel_path "<Actions>/ThunarLauncher/restore" "")
; (gtk_accel_path "<Actions>/ThunarBookmarks/78d0589179dc428e67218d99e42a9680" "")
; (gtk_accel_path "<Actions>/ThunarWindow/open-desktop" "")
; (gtk_accel_path "<Actions>/ThunarBookmarks/bb2d8ff4f98743e3e72aed7ebeee3b8d" "")
; (gtk_accel_path "<Actions>/ThunarStandardView/create-folder" "<Primary><Shift>n")
; (gtk_accel_path "<Actions>/ThunarWindow/open-location" "<Primary>l")
; (gtk_accel_path "<Actions>/ThunarWindow/view-menubar" "<Primary>m")
; (gtk_accel_path "<Actions>/ThunarWindow/zoom-reset-alt" "<Primary>0")
; (gtk_accel_path "<Actions>/ThunarWindow/view-as-detailed-list" "<Primary>2")
; (gtk_accel_path "<Actions>/ThunarWindow/view-as-icons" "<Primary>1")
; (gtk_accel_path "<Actions>/ThunarWindow/new-window" "<Primary>n")
; (gtk_accel_path "<Actions>/ThunarStandardView/forward" "<Alt>Right")
; (gtk_accel_path "<Actions>/ThunarLauncher/copy" "<Primary>c")
; (gtk_accel_path "<Actions>/ThunarStandardView/sort-descending" "")
; (gtk_accel_path "<Actions>/ThunarWindow/file-menu" "")
; (gtk_accel_path "<Actions>/ThunarStandardView/make-link" "")
(gtk_accel_path "<Actions>/ThunarActions/uca-action-1-1" "F4")
; (gtk_accel_path "<Actions>/ThunarWindow/new-tab" "<Primary>t")
; (gtk_accel_path "<Actions>/ThunarWindow/go-menu" "")
; (gtk_accel_path "<Actions>/ThunarStandardView/sort-by-size" "")
; (gtk_accel_path "<Actions>/ThunarStandardView/sort-by-mtime" "")
; (gtk_accel_path "<Actions>/ThunarWindow/view-location-selector-pathbar" "")
; (gtk_accel_path "<Actions>/ThunarWindow/show-hidden" "<Primary>h")
; (gtk_accel_path "<Actions>/ThunarStandardView/sort-by-name" "")
; (gtk_accel_path "<Actions>/ThunarWindow/zoom-reset" "<Primary>KP_0")
; (gtk_accel_path "<Actions>/ThunarLauncher/trash-delete-2" "KP_Delete")
; (gtk_accel_path "<Actions>/ThunarWindow/detach-tab" "")
