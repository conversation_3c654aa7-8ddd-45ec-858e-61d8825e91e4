; xfce4-terminal GtkAccelMap rc-file         -*- scheme -*-
; this file is an automated accelerator map dump
;
(gtk_accel_path "<Actions>/terminal-window/goto-tab-2" "<Alt>2")
(gtk_accel_path "<Actions>/terminal-window/goto-tab-6" "<Alt>6")
; (gtk_accel_path "<Actions>/terminal-window/copy-input" "")
; (gtk_accel_path "<Actions>/terminal-window/close-other-tabs" "")
; (gtk_accel_path "<Actions>/terminal-window/move-tab-right" "<Primary><Shift>Page_Down")
(gtk_accel_path "<Actions>/terminal-window/goto-tab-7" "<Alt>7")
; (gtk_accel_path "<Actions>/terminal-window/set-title-color" "")
; (gtk_accel_path "<Actions>/terminal-window/edit-menu" "")
; (gtk_accel_path "<Actions>/terminal-window/zoom-menu" "")
(gtk_accel_path "<Actions>/terminal-window/goto-tab-1" "<Alt>1")
; (gtk_accel_path "<Actions>/terminal-window/fullscreen" "F11")
; (gtk_accel_path "<Actions>/terminal-window/read-only" "")
(gtk_accel_path "<Actions>/terminal-window/goto-tab-5" "<Alt>5")
; (gtk_accel_path "<Actions>/terminal-window/preferences" "")
; (gtk_accel_path "<Actions>/terminal-window/reset-and-clear" "")
; (gtk_accel_path "<Actions>/terminal-window/about" "")
(gtk_accel_path "<Actions>/terminal-window/goto-tab-4" "<Alt>4")
; (gtk_accel_path "<Actions>/terminal-window/close-window" "<Primary><Shift>q")
; (gtk_accel_path "<Actions>/terminal-window/reset" "")
; (gtk_accel_path "<Actions>/terminal-window/save-contents" "")
(gtk_accel_path "<Actions>/terminal-window/toggle-menubar" "F10")
; (gtk_accel_path "<Actions>/terminal-window/copy" "<Primary><Shift>c")
; (gtk_accel_path "<Actions>/terminal-window/copy-html" "")
; (gtk_accel_path "<Actions>/terminal-window/last-active-tab" "")
; (gtk_accel_path "<Actions>/terminal-window/show-borders" "")
; (gtk_accel_path "<Actions>/terminal-window/view-menu" "")
; (gtk_accel_path "<Actions>/terminal-window/detach-tab" "<Primary><Shift>d")
; (gtk_accel_path "<Actions>/terminal-window/scroll-on-output" "")
; (gtk_accel_path "<Actions>/terminal-window/show-toolbar" "")
; (gtk_accel_path "<Actions>/terminal-window/next-tab" "<Primary>Page_Down")
; (gtk_accel_path "<Actions>/terminal-window/tabs-menu" "")
; (gtk_accel_path "<Actions>/terminal-window/search-next" "")
; (gtk_accel_path "<Actions>/terminal-window/search-prev" "")
; (gtk_accel_path "<Actions>/terminal-window/undo-close-tab" "")
; (gtk_accel_path "<Actions>/terminal-window/set-title" "<Primary><Shift>s")
; (gtk_accel_path "<Actions>/terminal-window/contents" "F1")
; (gtk_accel_path "<Actions>/terminal-window/zoom-reset" "<Primary>0")
; (gtk_accel_path "<Actions>/terminal-window/close-tab" "<Primary><Shift>w")
; (gtk_accel_path "<Actions>/terminal-window/new-tab" "<Primary><Shift>t")
; (gtk_accel_path "<Actions>/terminal-window/new-window" "<Primary><Shift>n")
; (gtk_accel_path "<Actions>/terminal-window/terminal-menu" "")
; (gtk_accel_path "<Actions>/terminal-window/show-menubar" "")
; (gtk_accel_path "<Actions>/terminal-window/select-all" "<Primary><Shift>a")
; (gtk_accel_path "<Actions>/terminal-window/paste" "<Primary><Shift>v")
(gtk_accel_path "<Actions>/terminal-window/goto-tab-9" "<Alt>9")
; (gtk_accel_path "<Actions>/terminal-window/move-tab-left" "<Primary><Shift>Page_Up")
; (gtk_accel_path "<Actions>/terminal-window/search" "<Primary><Shift>f")
; (gtk_accel_path "<Actions>/terminal-window/file-menu" "")
; (gtk_accel_path "<Actions>/terminal-window/prev-tab" "<Primary>Page_Up")
; (gtk_accel_path "<Actions>/terminal-window/paste-selection" "")
; (gtk_accel_path "<Actions>/terminal-window/zoom-in" "<Primary>plus")
; (gtk_accel_path "<Actions>/terminal-window/zoom-out" "<Primary>minus")
(gtk_accel_path "<Actions>/terminal-window/goto-tab-8" "<Alt>8")
; (gtk_accel_path "<Actions>/terminal-window/help-menu" "")
(gtk_accel_path "<Actions>/terminal-window/goto-tab-3" "<Alt>3")
