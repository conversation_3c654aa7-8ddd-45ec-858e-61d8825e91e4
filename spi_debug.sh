#!/bin/bash

echo "=== RK3588 SPI0 GPIO3_D组诊断脚本 ==="
echo ""

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}1. 检查SPI设备状态${NC}"
echo "SPI设备文件:"
ls -la /dev/spidev* 2>/dev/null || echo "未找到SPI设备"

echo ""
echo -e "${BLUE}2. 检查GPIO3_D组引脚状态 (125-128)${NC}"
echo "GPIO3_D1-D4引脚配置:"
cat /sys/kernel/debug/pinctrl/pinctrl-rockchip-pinctrl/pinmux-pins | grep -E "pin (125|126|127|128)"

echo ""
echo -e "${BLUE}3. 检查SPI0设备树状态${NC}"
echo "SPI0状态:"
if [ -f "/sys/firmware/devicetree/base/spi@feb00000/status" ]; then
    echo "Status: $(cat /sys/firmware/devicetree/base/spi@feb00000/status | tr -d '\0')"
else
    echo -e "${RED}SPI0设备树节点不存在${NC}"
fi

echo ""
echo "SPI0 pinctrl配置:"
if [ -f "/sys/firmware/devicetree/base/spi@feb00000/pinctrl-0" ]; then
    echo "pinctrl-0存在"
    xxd /sys/firmware/devicetree/base/spi@feb00000/pinctrl-0 | head -2
else
    echo -e "${RED}pinctrl-0不存在${NC}"
fi

echo ""
echo -e "${BLUE}4. 检查SPI驱动状态${NC}"
echo "SPI相关内核消息:"
dmesg | grep -i "feb00000.spi" || echo "未找到SPI0相关消息"

echo ""
echo -e "${BLUE}5. 检查GPIO3状态${NC}"
echo "GPIO3控制器状态:"
cat /sys/kernel/debug/gpio | grep -A20 "gpiochip3:" | grep -E "gpio-(125|126|127|128)"

echo ""
echo -e "${BLUE}6. 尝试手动导出GPIO进行测试${NC}"

# 尝试导出GPIO3_D1-D4
for pin in 125 126 127; do
    echo "导出GPIO $pin..."
    echo $pin > /sys/class/gpio/export 2>/dev/null || echo "GPIO $pin 导出失败"
done

sleep 1

# 检查导出结果
echo "导出的GPIO:"
ls /sys/class/gpio/ | grep gpio

echo ""
echo -e "${BLUE}7. 运行SPI测试${NC}"
if [ -f "./spi_test/simple_spi_test" ]; then
    echo "运行SPI测试程序..."
    ./spi_test/simple_spi_test 2>&1 | head -10
elif [ -f "./spi_master_test" ]; then
    echo "运行厂商SPI测试程序..."
    ./spi_master_test 2>&1 | head -10
else
    echo "未找到SPI测试程序"
fi

echo ""
echo -e "${BLUE}8. 问题分析${NC}"

# 分析问题
if cat /sys/kernel/debug/pinctrl/pinctrl-rockchip-pinctrl/pinmux-pins | grep -E "pin (125|126|127)" | grep -q "UNCLAIMED"; then
    echo -e "${RED}问题: GPIO3_D引脚未被正确配置为SPI功能${NC}"
    echo "可能原因:"
    echo "1. 设备树配置未生效"
    echo "2. 功能复用号不正确"
    echo "3. SPI0控制器未启用"
fi

if ! dmesg | grep -q "feb00000.spi.*probed"; then
    echo -e "${RED}问题: SPI0控制器未正确初始化${NC}"
    echo "建议检查设备树中的SPI0节点配置"
fi

echo ""
echo -e "${BLUE}9. 建议的修复步骤${NC}"
echo "1. 确认设备树中SPI0节点的status为'okay'"
echo "2. 确认pinctrl配置使用正确的功能号"
echo "3. 重新编译并替换设备树文件"
echo "4. 重启系统验证修复效果"

# 清理导出的GPIO
echo ""
echo "清理导出的GPIO..."
for pin in 125 126 127; do
    echo $pin > /sys/class/gpio/unexport 2>/dev/null || true
done
