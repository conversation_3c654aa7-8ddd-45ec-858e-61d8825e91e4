// GPIO3_D组 SPI0 设备树修复配置
// 基于GPIO3_D1-D4引脚

&pinctrl {
    spi0 {
        spi0m3_pins: spi0m3-pins {
            rockchip,pins =
                /* SPI0_CLK_M3: GPIO3_D3, 需要确认正确的功能号 */
                <3 RK_PD3 4 &pcfg_pull_up_drv_level_2>,
                /* SPI0_MISO_M3: GPIO3_D1, 需要确认正确的功能号 */
                <3 RK_PD1 4 &pcfg_pull_up>,
                /* SPI0_MOSI_M3: GPIO3_D2, 需要确认正确的功能号 */
                <3 RK_PD2 4 &pcfg_pull_up_drv_level_2>;
        };
        
        spi0m3_cs0: spi0m3-cs0 {
            rockchip,pins =
                /* SPI0_CS0_M3: GPIO3_D4, 需要确认正确的功能号 */
                <3 RK_PD4 4 &pcfg_pull_up>;
        };
    };
};

&spi0 {
    status = "okay";
    pinctrl-names = "default";
    pinctrl-0 = <&spi0m3_pins>, <&spi0m3_cs0>;
    
    spi-max-frequency = <50000000>;
    
    spidev@0 {
        compatible = "rohm,dh2228fv";
        reg = <0>;
        spi-max-frequency = <50000000>;
    };
};
