#include <stdio.h>
#include <stdlib.h>
#include <unistd.h>
#include <fcntl.h>
#include <sys/ioctl.h>
#include <linux/spi/spidev.h>
#include <stdint.h>
#include <string.h>

#define SPI_DEVICE "/dev/spidev0.0"

int main()
{
    int fd;
    int ret;
    uint8_t mode = SPI_MODE_0;
    uint8_t bits = 8;
    uint32_t speed = 1000000; // 1MHz
    uint16_t delay = 0;
    
    // 测试数据
    uint8_t tx_data[] = {0x01, 0x02, 0x03, 0x04, 0x05};
    uint8_t rx_data[sizeof(tx_data)] = {0};
    
    printf("=== RK3588 SPI 主机测试程序 ===\n");
    printf("设备: %s\n", SPI_DEVICE);
    
    // 打开SPI设备
    fd = open(SPI_DEVICE, O_RDWR);
    if (fd < 0) {
        perror("无法打开SPI设备");
        return -1;
    }
    
    // 设置SPI模式
    ret = ioctl(fd, SPI_IOC_WR_MODE, &mode);
    if (ret == -1) {
        perror("无法设置SPI模式");
        close(fd);
        return -1;
    }
    
    // 设置每字位数
    ret = ioctl(fd, SPI_IOC_WR_BITS_PER_WORD, &bits);
    if (ret == -1) {
        perror("无法设置位数");
        close(fd);
        return -1;
    }
    
    // 设置最大速度
    ret = ioctl(fd, SPI_IOC_WR_MAX_SPEED_HZ, &speed);
    if (ret == -1) {
        perror("无法设置速度");
        close(fd);
        return -1;
    }
    
    printf("SPI配置成功:\n");
    printf("- 模式: %d\n", mode);
    printf("- 位数: %d\n", bits);
    printf("- 速度: %d Hz\n", speed);
    
    // 准备传输结构
    struct spi_ioc_transfer tr = {
        .tx_buf = (unsigned long)tx_data,
        .rx_buf = (unsigned long)rx_data,
        .len = sizeof(tx_data),
        .delay_usecs = delay,
        .speed_hz = speed,
        .bits_per_word = bits,
    };
    
    // 执行SPI传输
    printf("\n开始SPI传输...\n");
    ret = ioctl(fd, SPI_IOC_MESSAGE(1), &tr);
    if (ret < 1) {
        perror("SPI传输失败");
        close(fd);
        return -1;
    }
    
    // 显示结果
    printf("传输成功!\n");
    printf("发送数据: ");
    for (int i = 0; i < sizeof(tx_data); i++) {
        printf("0x%02X ", tx_data[i]);
    }
    printf("\n");
    
    printf("接收数据: ");
    for (int i = 0; i < sizeof(rx_data); i++) {
        printf("0x%02X ", rx_data[i]);
    }
    printf("\n");
    
    // 循环测试模式
    printf("\n进入循环测试模式 (按Ctrl+C退出)...\n");
    int count = 0;
    while (1) {
        // 更新发送数据
        for (int i = 0; i < sizeof(tx_data); i++) {
            tx_data[i] = (count + i) & 0xFF;
        }
        
        // 清空接收缓冲区
        memset(rx_data, 0, sizeof(rx_data));
        
        // 执行传输
        ret = ioctl(fd, SPI_IOC_MESSAGE(1), &tr);
        if (ret < 1) {
            perror("SPI传输失败");
            break;
        }
        
        printf("测试 #%d - TX: ", count);
        for (int i = 0; i < sizeof(tx_data); i++) {
            printf("%02X ", tx_data[i]);
        }
        printf("RX: ");
        for (int i = 0; i < sizeof(rx_data); i++) {
            printf("%02X ", rx_data[i]);
        }
        printf("\n");
        
        count++;
        usleep(500000); // 500ms延时
    }
    
    close(fd);
    return 0;
}
