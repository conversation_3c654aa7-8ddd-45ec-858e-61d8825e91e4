#!/bin/bash

echo "=== RK3588 风扇控制测试脚本 ==="
echo ""

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}1. 检查PWM设备${NC}"
echo "可用的PWM芯片:"
ls -la /sys/class/pwm/

echo ""
echo -e "${BLUE}2. 检查温度传感器${NC}"
echo "可用的温度区域:"
for zone in /sys/class/thermal/thermal_zone*; do
    if [ -f "$zone/temp" ]; then
        temp=$(cat "$zone/temp" 2>/dev/null)
        temp_c=$((temp / 1000))
        echo "$(basename $zone): ${temp_c}°C"
    fi
done

echo ""
echo -e "${BLUE}3. 编译风扇控制程序${NC}"
make clean
make

if [ $? -eq 0 ]; then
    echo -e "${GREEN}编译成功!${NC}"
else
    echo -e "${RED}编译失败!${NC}"
    exit 1
fi

echo ""
echo -e "${BLUE}4. 程序使用说明${NC}"
echo "可用的命令:"
echo "  sudo ./fan_control -h          # 显示帮助"
echo "  sudo ./fan_control -s 100      # 手动设置风扇速度为100"
echo "  sudo ./fan_control -t          # 测试模式"
echo "  sudo ./fan_control -a          # 自动模式"

echo ""
echo -e "${BLUE}5. 快速测试${NC}"
echo "运行快速测试 (设置风扇速度为100，持续3秒)..."

sudo ./fan_control -s 100
sleep 3
sudo ./fan_control -s 0

echo ""
echo -e "${GREEN}测试完成!${NC}"
echo ""
echo -e "${YELLOW}使用建议:${NC}"
echo "1. 使用测试模式观察风扇转速变化: sudo ./fan_control -t"
echo "2. 使用自动模式让系统根据温度自动调节: sudo ./fan_control -a"
echo "3. 手动设置特定速度: sudo ./fan_control -s <0-255>"
