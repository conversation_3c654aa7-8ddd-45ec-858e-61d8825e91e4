#!/bin/bash

echo "=== GPIO3_D组SPI功能号测试脚本 ==="
echo ""

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}1. 当前GPIO3_D组状态${NC}"
echo "GPIO3_D1-D4 (125-128) 当前配置:"
cat /sys/kernel/debug/pinctrl/pinctrl-rockchip-pinctrl/pinmux-pins | grep -E "pin (125|126|127|128)"

echo ""
echo -e "${BLUE}2. 尝试释放被占用的GPIO${NC}"

# 尝试释放GPIO
for pin in 125 126 127; do
    if [ -d "/sys/class/gpio/gpio$pin" ]; then
        echo "尝试释放GPIO $pin..."
        echo $pin > /sys/class/gpio/unexport 2>/dev/null || echo "GPIO $pin 释放失败"
    fi
done

echo ""
echo -e "${BLUE}3. 查找可能占用GPIO的设备树节点${NC}"

# 查找可能的GPIO配置
echo "查找设备树中的GPIO配置..."
find /sys/firmware/devicetree/base -name "*" -exec grep -l "gpio3" {} \; 2>/dev/null | while read file; do
    if xxd "$file" 2>/dev/null | grep -q "1d\|1e\|1f"; then  # 29,30,31的十六进制
        echo "可能相关的设备树节点: $file"
        xxd "$file" | head -2
    fi
done

echo ""
echo -e "${BLUE}4. 检查GPIO3D的IOMUX寄存器${NC}"

# 检查是否有devmem工具
if command -v devmem &> /dev/null; then
    echo "读取GPIO3D IOMUX寄存器 (0xFD5F4024):"
    current_val=$(devmem 0xFD5F4024 32 2>/dev/null || echo "无法读取")
    echo "当前值: $current_val"
    
    if [ "$current_val" != "无法读取" ]; then
        echo "解析:"
        echo "  GPIO3_D1[7:4]  = $((($current_val >> 4) & 0xF))"
        echo "  GPIO3_D2[11:8] = $((($current_val >> 8) & 0xF))"
        echo "  GPIO3_D3[15:12]= $((($current_val >> 12) & 0xF))"
        echo "  GPIO3_D4[19:16]= $((($current_val >> 16) & 0xF))"
    fi
else
    echo "devmem工具不可用，无法读取寄存器"
fi

echo ""
echo -e "${BLUE}5. 生成测试用的设备树片段${NC}"

# 生成不同功能号的设备树配置
for func in 1 2 3 4 5 6 7; do
    cat > "gpio3d_spi_func${func}.dts" << EOF
// GPIO3_D组 SPI0 功能号${func} 测试配置
&pinctrl {
    spi0 {
        spi0m3_pins_test: spi0m3-pins-test {
            rockchip,pins =
                <3 RK_PD1 ${func} &pcfg_pull_up_drv_level_2>,  // GPIO3_D1 CLK
                <3 RK_PD2 ${func} &pcfg_pull_up>,              // GPIO3_D2 MISO
                <3 RK_PD3 ${func} &pcfg_pull_up_drv_level_2>;  // GPIO3_D3 MOSI
        };
        
        spi0m3_cs0_test: spi0m3-cs0-test {
            rockchip,pins =
                <4 RK_PA0 ${func} &pcfg_pull_up>;              // GPIO4_A0 CS0 (原GPIO3_D4)
        };
    };
};

&spi0 {
    status = "okay";
    pinctrl-names = "default";
    pinctrl-0 = <&spi0m3_pins_test>, <&spi0m3_cs0_test>;
    
    spidev@0 {
        compatible = "rohm,dh2228fv";
        reg = <0>;
        spi-max-frequency = <50000000>;
    };
};
EOF
    echo "生成测试配置: gpio3d_spi_func${func}.dts (功能号${func})"
done

echo ""
echo -e "${BLUE}6. 建议的测试步骤${NC}"
echo "1. 选择一个功能号配置文件 (gpio3d_spi_func1.dts 到 gpio3d_spi_func7.dts)"
echo "2. 将配置添加到您的主设备树文件中"
echo "3. 重新编译设备树: dtc -I dts -O dtb -o board.dtb board.dts"
echo "4. 替换设备树文件并重启"
echo "5. 验证配置: cat /sys/kernel/debug/pinctrl/pinctrl-rockchip-pinctrl/pinmux-pins | grep -E \"pin (125|126|127|128)\""

echo ""
echo -e "${BLUE}7. 强制释放GPIO的方法${NC}"

cat > "force_release_gpio3d.sh" << 'EOF'
#!/bin/bash
echo "强制释放GPIO3_D1-D3..."

# 方法1: 直接操作sysfs
for pin in 125 126 127; do
    echo $pin > /sys/class/gpio/unexport 2>/dev/null || true
done

# 方法2: 重新绑定相关驱动
echo "重新绑定pinctrl驱动..."
echo "pinctrl-rockchip-pinctrl" > /sys/bus/platform/drivers/pinctrl-rockchip/unbind 2>/dev/null || true
sleep 1
echo "pinctrl-rockchip-pinctrl" > /sys/bus/platform/drivers/pinctrl-rockchip/bind 2>/dev/null || true

echo "重新绑定SPI驱动..."
echo "feb00000.spi" > /sys/bus/platform/drivers/rockchip-spi/unbind 2>/dev/null || true
sleep 1
echo "feb00000.spi" > /sys/bus/platform/drivers/rockchip-spi/bind 2>/dev/null || true

echo "完成!"
EOF

chmod +x force_release_gpio3d.sh
echo "生成强制释放脚本: force_release_gpio3d.sh"

echo ""
echo -e "${GREEN}脚本执行完成!${NC}"
echo ""
echo -e "${YELLOW}下一步建议:${NC}"
echo "1. 尝试运行: sudo ./force_release_gpio3d.sh"
echo "2. 选择合适的功能号配置文件"
echo "3. 修改您的设备树文件"
echo "4. 重新编译并测试"

echo ""
echo -e "${YELLOW}注意事项:${NC}"
echo "- GPIO3_D4实际上是GPIO4_A0，需要特别处理"
echo "- 功能号可能需要查阅RK3588技术参考手册确认"
echo "- 建议先备份当前工作的设备树配置"
